INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01,SBE01,<PERSON>TL01,INFI01,PPB01,<PERSON>HA01,BHA001,<PERSON><PERSON><PERSON>01,TIV001,SPB001,HSG001,SAM001,BHARAT01,ORIC01,METRO01,ATC01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,BTVL01,SSPV001,ARIA01,CAHLLP01,DYDX001', 'CE2', 'QIB', '**************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf8', 'Pro', 'CI4', 'QIB', '************', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf8', 'Pro', 'CI4', 'QIB', '************', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10', 'Pro', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF10_Akhil', 'Pro', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10(settlement)', 'Pro', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10(settlement)', 'Pro', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF1', 'QICM01', 'CI4', 'QIB', '************', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF1', 'QICM01', 'CI4', 'QIB', '*************', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf8', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF55', 'Pro', 'CI4', 'QIB', '*************', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF55', 'Pro', 'CI4', 'QIB', '*************', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01,SBE01,STTL01,INFI01,PPB01,MAHA01,BHA001,EIPL01,TIV001,SPB001,HSG001,SAM001,BHARAT01,ORIC01,METRO01,ATC01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,BTVL01,SSPV001,ARIA01,CAHLLP01,DYDX001', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CI4', 'QIB', '*************', 40, '2025-06-01', '2025-06-12', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('CZ_TEST_PRO', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-13', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('LFT', 'QICM01,WWLLP,INFI01', 'CI4', 'QIB', '*************', 40, '2025-06-01', '2025-06-22', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01,WWLLP,INFI01', 'CI4', 'QIB', '*************', 40, '2025-06-23', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CI4', 'QIB', '*************', 40, '2025-06-01', '2025-06-12', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('CZ_TEST_CP', 'QICM01', 'CI4', 'QIB', '*************', 40, '2025-06-13', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 39, '2025-06-01', '2025-06-18', 39, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_Vajra', 'Pro', 'CI4', 'QIB', '*************', 39, '2025-06-19', NULL, 39, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 1, '2025-06-01', NULL, 0, 1, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CI4', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CI4', 'QIB', '************5', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10(settlement)', 'Pro', 'CI4', 'QIB', '************6', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************7', 39, '2025-06-01', NULL, 0, 39, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************7', 1, '2025-06-01', NULL, 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CI4', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CI4', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF55', 'Pro', 'CI4', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF55', 'Pro', 'CI4', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10(settlement)', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Magmio Testing', '', 'CI4', 'QIB', '*************', 40, '2025-06-01', '2025-06-19', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01,SBE01,STTL01,INFI01,PPB01,MAHA01,BHA001,EIPL01,TIV001,SPB001,HSG001,SAM001,BHARAT01,ORIC01,METRO01,ATC01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,BTVL01,SSPV001,ARIA01,CAHLLP01,DYDX001', 'CI4', 'QIB', '*************', 40, '2025-06-20', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Greeks soft', 'QICM01_CP/Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf8', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'CI4', 'QIB', '************', 1000, '2025-06-01', '2025-06-09', 1000, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************', 40, '2025-06-10', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('BULLBEAR', 'Pro', 'CI4', 'QIB', '************', 100, '2025-06-01', NULL, 100, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('BRAHMASTRA', 'SUBH001,EIPL01,SHUB001,FFPL01', 'CI4', 'QIB', '*************', 200, '2025-06-01', NULL, 200, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CI4', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CI4', 'QIB', '**************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01,SBE01,STTL01,INFI01,PPB01,MAHA01,BHA001,EIPL01,TIV001,SPB001,HSG001,SAM001,BHARAT01,ORIC01,METRO01,ATC01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,BTVL01,SSPV001,ARIA01,CAHLLP01,DYDX001', 'CI4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF28', 'Pro', 'BB11', 'QIB', '*************', 200, '2025-06-01', NULL, 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'BB11', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'BB11', 'QIB', '*************', 39, '2025-06-01', NULL, 39, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'BB11', 'QIB', '*************', 1, '2025-06-01', NULL, 0, 1, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF28', 'Pro', 'BB11', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF28', 'Pro', 'BB11', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF28', 'Pro', 'BB11', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF28', 'Pro', 'BB11', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General(120 fpga testing)', 'QICM01_CP', 'BB11', 'QIB', '***********', 39, '2025-06-01', NULL, 39, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '***********', 1, '2025-06-01', NULL, 0, 1, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'BB11', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,SBE01,MAHA01,BHA001, TIV001, SAL01,JYOTI01,ATC01,KPL01', 'CK4', 'QIB', '************', 1000, '2025-06-01', '2025-06-03', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,SBE01,MAHA01,BHA001, TIV001, SAL01,JYOTI01,ATC01,KPL01,TECH01', 'CK4', 'QIB', '************', 1000, '2025-06-04', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001,ARIA01,KGR001,BTVL01, KGR001, CAHLLP01, BHH009, KPL01,DYDX001, HARSH01', 'CK4', 'QIB', '************', 1000, '2025-06-01', '2025-06-05', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001,ARIA01,KGR001,BTVL01, KGR001, CAHLLP01, BHH009, KPL01,DYDX001, HARSH01,ADS01', 'CK4', 'QIB', '************', 1000, '2025-06-06', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01,SYLI01,RAH01,STTL01,ARV001,NITA001,DYDX001,HARSH01,BTVL01, KGR001', 'CK4', 'QIB', '************', 1000, '2025-06-01', '2025-06-05', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01,SYLI01,RAH01,STTL01,ARV001,NITA001,DYDX001,HARSH01,BTVL01, KGR001,DEV001', 'CK4', 'QIB', '************', 1000, '2025-06-06', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KIRIT001,SPB001,SVS01,HSG001,SAM001,METRO01,AMPL01,KVEE01,CAHLLP01,FFPL01, KPL01', 'CK4', 'QIB', '************', 1000, '2025-06-01', '2025-06-05', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KIRIT001,SPB001,SVS01,HSG001,SAM001,METRO01,AMPL01,KVEE01,CAHLLP01,FFPL01, KPL01,SMC01', 'CK4', 'QIB', '************', 1000, '2025-06-06', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '************', 1, '2025-06-01', NULL, 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf16', 'Pro', 'CK4', 'QIB', '************', 99, '2025-06-01', NULL, 0, 99, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MIPL01', 'CK4', 'QIB', '************', 200, '2025-06-01', NULL, 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MIPL01', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'SSS01', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MODERN', 'CK4', 'QIB', '************', 100, '2025-06-01', NULL, 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KPPL01', 'CK4', 'QIB', '************', 100, '2025-06-01', NULL, 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KPPL01', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MODERN', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MODERN', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'SSS01', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('EXECUTOR', 'QICM01_CP/QICM01', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_PRO', 'Pro', 'CK4', 'QIB', '************', 200, '2025-06-01', NULL, 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_PRO', 'Pro', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'CRIMSON', 'CK4', 'QIB', '************', 100, '2025-06-01', NULL, 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'HUSH01', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'HUSH01', 'CK4', 'QIB', '************', 200, '2025-06-01', NULL, 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01,SYLI01,RAH01,STTL01,ARV001,NITA001,DYDX001,HARSH01', 'CK4', 'QIB', '************', 40, '2025-06-01', '2025-06-05', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01,SYLI01,RAH01,STTL01,ARV001,NITA001,DYDX001,HARSH01,DEV001', 'CK4', 'QIB', '************', 40, '2025-06-06', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,QICM01,SBE01,MAHA01,BHA001,TIV001, SAL01,JYOTI01,ATC01,KPL01', 'CK4', 'QIB', '************', 40, '2025-06-01', '2025-06-03', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,QICM01,SBE01,MAHA01,BHA001,TIV001, SAL01,JYOTI01,ATC01,KPL01,TECH01', 'CK4', 'QIB', '************', 40, '2025-06-04', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001,ARIA01,FFPL01,KGR001', 'CK4', 'QIB', '************', 40, '2025-06-01', '2025-06-05', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001,ARIA01,FFPL01,KGR001,ADS01', 'CK4', 'QIB', '************', 40, '2025-06-06', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MODERN', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KIRIT001,SPB001,SVS01,HSG001,SAM001,METRO01,AMPL01,KVEE01,CAHLLP01', 'CK4', 'QIB', '************', 40, '2025-06-01', '2025-06-05', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KIRIT001,SPB001,SVS01,HSG001,SAM001,METRO01,AMPL01,KVEE01,CAHLLP01,SMC01', 'CK4', 'QIB', '************', 40, '2025-06-06', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('EXECUTOR', 'Pro', 'CK4', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'HUSH01', 'CK4', 'QIB', '***********7', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'CRIMSON', 'CK4', 'QIB', '***********8', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'CRIMSON', 'CK4', 'QIB', '***********9', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'CRIMSON', 'CK4', 'QIB', '***********0', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_PRO', 'Pro', 'CK4', 'QIB', '***********1', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_PRO', 'Pro', 'CK4', 'QIB', '***********2', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MIPL01', 'CK4', 'QIB', '***********3', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MIPL01', 'CK4', 'QIB', '***********4', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********5', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'HUSH01', 'CK4', 'QIB', '***********6', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KPPL01', 'CK4', 'QIB', '***********7', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KPPL01', 'CK4', 'QIB', '***********8', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********9', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********0', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********1', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'INFI01', 'CK4', 'QIB', '***********2', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '***********3', 1, '2025-06-01', NULL, 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '***********3', 39, '2025-06-01', '2025-06-19', 0, 39, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('LFT', 'QICM01, WWLLP, INFI01', 'CK4', 'QIB', '***********3', 39, '2025-06-20', NULL, 0, 39, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '***********4', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'INFI01', 'CK4', 'QIB', '***********5', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '***********6', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '***********7', 40, '2025-06-01', '2025-06-18', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF16', 'Pro', 'CK4', 'QIB', '***********7', 400, '2025-06-19', NULL, 0, 400, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '***********8', 40, '2025-06-01', '2025-06-18', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF16', 'Pro', 'CK4', 'QIB', '***********8', 400, '2025-06-19', NULL, 0, 400, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF16', 'Pro', 'CK4', 'QIB', '***********9', 100, '2025-06-01', '2025-06-24', 0, 100, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF16', 'Pro', 'CK4', 'QIB', '***********9', 400, '2025-06-25', NULL, 0, 400, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_atled_bv', 'Pro', 'CK3', 'QIB', '**********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '**********', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK3', 'QIB', '**********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf19', 'QICM01', 'CK3', 'QIB', '**********', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '**********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf23', 'Pro', 'CK3', 'QIB', '**********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '***********', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf23', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '***********', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '***********', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '***********', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('SEVEN ALPHA', 'SVP01', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('SEVEN ALPHA', 'SVP', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_ATLED_AK', 'QICM01', 'CK3', 'QIB', '**********0', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CK3', 'QIB', '**********1', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '**********2', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********3', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********4', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********5', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********6', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********7', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'HUSH01', 'CK3', 'QIB', '**********8', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf23', 'Pro', 'CK3', 'QIB', '**********9', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf23', 'Pro', 'CK3', 'QIB', '**********0', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CK3', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CK3', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CK3', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CK3', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 20, '2025-06-01', NULL, 20, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 20, '2025-06-01', NULL, 0, 20, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-06-01', '2025-06-04', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 1000, '2025-06-05', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf25', 'AAN001', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', '2025-06-11', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF31', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-12', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '***********', 100, '2025-06-01', NULL, 100, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '***********', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'HUSH01', 'CM2', 'QIB', '*************', 40, '2025-06-01', '2025-06-02', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF29', 'HUSH01', 'CM2', 'QIB', '*************', 40, '2025-06-03', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'HUSH01', 'CM2', 'QIB', '*************', 40, '2025-06-05', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'HUSH01', 'CM2', 'QIB', '*************', 40, '2025-06-05', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DE5', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DE5', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'DE5', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'DE5', 'QIB', '**************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'DE5', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'DE5', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'DE5', 'QIB', '**************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'DE5', 'QIB', '**************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF24', 'Pro', 'DA6', 'QIB', '************', 1000, '2025-06-01', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 400, '2025-06-01', '2025-06-04', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 1000, '2025-06-05', NULL, 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_ak', 'Pro', 'DA6', 'QIB', '************', 1000, '2025-06-01', NULL, 0, 1000, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_ak', 'Pro', 'DA6', 'QIB', '************', 1000, '2025-06-01', NULL, 0, 1000, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'SAM001, SYLI01,METRO01,ORIC01, SBE01, SHUB001, AMPL01, SUBH001, KVEE01, STTL01, ARV001, MTSL01, BHARAT01, SVS01, SSS01, EIPL01, FPPL01', 'DA6', 'QIB', '************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'METRO01, ORIC01, SBE01, SHUB001, AMPL01, SUBH001, KVEE01, STTL01, ARV001, MTSL01, BHARAT01, SVS01, SAM001, SYLI01,  SSS01, EIPL01, FPPL01', 'DA6', 'QIB', '************', 400, '2025-06-01', NULL, 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF24', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_ATLED_AK', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '************', 1, '2025-06-01', NULL, 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_ALTED_BV', 'Pro', 'DA6', 'QIB', '************', 399, '2025-06-01', NULL, 0, 399, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF5_D_PRO', 'Pro', 'DA6', 'QIB', '************', 100, '2025-06-01', NULL, 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF5_D_PRO', 'Pro', 'DA6', 'QIB', '************', 200, '2025-06-01', NULL, 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF5_D_PRO', 'Pro', 'DA6', 'QIB', '************', 100, '2025-06-01', NULL, 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01_CP', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'Pro', 'DA6', 'QIB', '************', 200, '2025-06-01', NULL, 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '**************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '**************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '************', 100, '2025-06-01', NULL, 100, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01,SBE01,STTL01,INFI01,PPB01,MAHA01,BHA001,EIPL01,TIV001,SPB001,HSG001,SAM001,BHARAT01,ORIC01,METRO01,ATC01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,BTVL01,SSPV001,ARIA01,CAHLLP01,DYDX001', 'DA6', 'QIB', '**************', 40, '2025-06-01', NULL, 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21_ART01', 'ART01', 'DA6', 'QIB', '************', 200, '2025-06-01', NULL, 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21_CRIMSON', 'CRIMSON', 'DA6', 'QIB', '************', 200, '2025-06-01', NULL, 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21_MODERN', 'MODERN', 'DA6', 'QIB', '************', 200, '2025-06-01', NULL, 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-06-01', '2025-06-11', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF5D', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-12', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-06-01', '2025-06-11', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF5D', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-12', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-06-01', '2025-06-11', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF5D', 'Pro', 'DA6', 'QIB', '************', 40, '2025-06-12', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'F8', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'F8', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'F8', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'F8', 'QIB', '*************', 40, '2025-06-01', NULL, 40, 0, 'Switch');
