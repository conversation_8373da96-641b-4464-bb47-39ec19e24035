#!/bin/bash
# Output file for SQL insert statements
OUTPUT_FILE="insert_statements05.sql"
# Clear the output file if it exists
> "$OUTPUT_FILE"

# Function to escape single quotes for SQL strings
escape_sql_string() {
    echo "$1" | sed "s/'/''/g"
}

# Skip header and read the CSV line by line
tail -n +2 "OPS-NSE0525.csv" | while IFS= read -r line; do
    # Use awk to parse the CSV line, respecting double quotes
    parsed_fields=$(echo "$line" | awk -F',' '{
        delete a;
        n = 0;
        in_quote = 0;
        current_field = "";
        for (i=1; i<=NF; i++) {
            f = $i;
            if (in_quote) {
                current_field = current_field "," f;
                if (substr(f, length(f), 1) == "\"") {
                    current_field = substr(current_field, 1, length(current_field)-1);
                    a[++n] = current_field;
                    current_field = "";
                    in_quote = 0;
                }
            } else {
                if (substr(f, 1, 1) == "\"" && substr(f, length(f), 1) != "\"") {
                    in_quote = 1;
                    current_field = substr(f, 2);
                } else if (substr(f, 1, 1) == "\"" && substr(f, length(f), 1) == "\"") {
                    a[++n] = substr(f, 2, length(f)-2);
                } else {
                    a[++n] = f;
                }
            }
        }
        for (j=1; j<=n; j++) {
            gsub(/^[[:space:]]+|[[:space:]]+$/, "", a[j]);
            print a[j];
        }
    }')
    
    # Read the parsed fields back into variables
    readarray -t fields <<< "$parsed_fields"
    
    # Map parsed fields to variable names based on new CSV headers
    OPS="${fields[0]}"
    CTCL="${fields[1]}"
    TAP_IP="${fields[2]}"
    BILLING_FROM="${fields[3]}"
    BILLING_UPTO="${fields[4]}"
    CLIENT="${fields[5]}"
    USER="${fields[6]}"
    REMARKS="${fields[7]}"
    RACK="${fields[8]}"
    SEGMENT="${fields[9]}"
    LINK_TYPE="${fields[10]}"
    
    # Set fixed value for Firm_Name
    FIRM_NAME="QIB"
    
    # Condition 1: trader_id is same as USER or CLIENT if USER is empty
    if [ -z "$USER" ]; then
        TRADER_ID="$(escape_sql_string "$CLIENT")"
    else
        TRADER_ID="$(escape_sql_string "$USER")"
    fi
    
    # Client_ID is set to CLIENT field
    CLIENT_ID="$(escape_sql_string "$CLIENT")"
    
    
    # Format dates to YYYY-MM-DD
    START_DATE_SQL="NULL"
    if [[ -n "$BILLING_FROM" ]]; then
        FORMATTED_DATE=$(date -d "$(echo "$BILLING_FROM" | sed 's/\([0-9]\{2\}\)\/\([0-9]\{2\}\)\/\([0-9]\{4\}\)/\3-\2-\1/')" +%Y-%m-%d)
        START_DATE_SQL="'$FORMATTED_DATE'"
    fi
    
    END_DATE_SQL="NULL"
    if [[ -n "$BILLING_UPTO" ]]; then
        FORMATTED_DATE=$(date -d "$(echo "$BILLING_UPTO" | sed 's/\([0-9]\{2\}\)\/\([0-9]\{2\}\)\/\([0-9]\{4\}\)/\3-\2-\1/')" +%Y-%m-%d)
        END_DATE_SQL="'$FORMATTED_DATE'"
    fi
    
    # Set FO_OPS and CM_OPS based on segment
    if [ "$SEGMENT" == "FNO" ]; then
        FO_OPS="$OPS"
        CM_OPS="0"
    elif [ "$SEGMENT" == "CM" ]; then
        FO_OPS="0"
        CM_OPS="$OPS"
    else
        FO_OPS="NULL"
        CM_OPS="NULL"
    fi
    
    # Construct the SQL INSERT statement
    SQL_STATEMENT="INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('$TRADER_ID', '$CLIENT_ID', '$RACK', '$FIRM_NAME', '$TAP_IP', $OPS, $START_DATE_SQL, $END_DATE_SQL, $FO_OPS, $CM_OPS, '$LINK_TYPE');"
    
    # Append to the output file
    echo "$SQL_STATEMENT" >> "$OUTPUT_FILE"
done

echo "SQL insert statements generated in $OUTPUT_FILE"
