INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_<PERSON>, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Drop Copy', '', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('fractal excutor', 'Pro', 'AW18', 'QIB', '**************', 30, '2025-04-01', '2025-04-30', 30, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('fractal_technical', 'Pro', 'AW18', 'QIB', '**************', 10, '2025-04-01', '2025-04-30', 0, 10, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 1, '2025-04-01', '2025-04-30', 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 399, '2025-04-01', '2025-04-30', 0, 399, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('TWAP(Fractal)', '', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf21', 'SVS,SYLI01,SSS01', 'AW18', 'QIB', '**************', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf7_AS', 'Pro', 'AW18', 'QIB', '**************', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'MTSL01,METRO01 ,SBE01,ORIC01,SHUB001, AMPL01, SUBH001, ARV001, STTL01, KVEE01 ,BHARAT01, EIPL01, SSS01, SVS01, SYLI01, SAM001', 'AW18', 'QIB', '**************', 200, '2025-04-01', '2025-04-01', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'MTSL01,METRO01 ,SBE01,ORIC01,SHUB001, AMPL01, SUBH001, ARV001, STTL01, KVEE01 ,BHARAT01, EIPL01, SSS01, SVS01, SYLI01, SAM001 , FPPL01', 'AW18', 'QIB', '**************', 200, '2025-04-02', '2025-04-10', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'QICM01', 'AW18', 'QIB', '**************', 200, '2025-04-11', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf7_Rajesh', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf7_MIPL', 'MIPL', 'AW18', 'QIB', '**************', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'VPB001', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-10', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF1(Vajra)', 'QICM01', 'AW18', 'QIB', '**************', 40, '2025-04-11', '2025-04-11', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'AW18', 'QIB', '**************', 40, '2025-04-12', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 1, '2025-04-01', '2025-04-30', 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf7_SAR001', 'SAR001', 'AW18', 'QIB', '**************', 39, '2025-04-01', '2025-04-30', 0, 39, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('BRAHMASTRA', 'Pro', 'AW18', 'QIB', '**************', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 1, '2025-04-01', '2025-04-30', 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 399, '2025-04-01', '2025-04-30', 0, 399, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 1, '2025-04-01', '2025-04-30', 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'AW18', 'QIB', '**************', 39, '2025-04-01', '2025-04-30', 0, 39, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 1, '2025-04-01', '2025-04-30', 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '**************', 39, '2025-04-01', '2025-04-30', 0, 39, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7 Fractal(UP)', 'QICM01_CP', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7 Fractal', 'Fractalarb', 'AW18', 'QIB', '**************', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf21_ART01', 'ART01', 'AW18', 'QIB', '**************', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf21_CRIMSON', 'CRIMSON', 'AW18', 'QIB', '**************', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf21_MODERN', 'MODERN', 'AW18', 'QIB', '**************', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf21_MIPL01', 'MIPL01', 'AW18', 'QIB', '**************', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf7_MIPL01', 'MIPL01', 'AW18', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '***********', 40, '2025-04-02', '2025-04-08', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'AW18', 'QIB', '***********', 40, '2025-04-09', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '***********', 40, '2025-04-11', '2025-04-16', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'AW18', 'QIB', '***********', 40, '2025-04-17', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '*************', 40, '2025-04-02', '2025-04-08', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'AW18', 'QIB', '*************', 40, '2025-04-09', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '*************', 40, '2025-04-11', '2025-04-16', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'AW18', 'QIB', '*************', 40, '2025-04-17', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '***********', 40, '2025-04-02', '2025-04-29', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'AW18', 'QIB', '***********', 40, '2025-04-30', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'AW18', 'QIB', '***********', 40, '2025-04-02', '2025-04-29', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'AW18', 'QIB', '***********', 40, '2025-04-30', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'BO1', 'QIB', '**************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'BO1', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf1', 'QICM01', 'CE2', 'QIB', '**************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf1', 'QICM01', 'CE2', 'QIB', '**************', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf1', 'QICM01', 'CE2', 'QIB', '**************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF30', 'QICM01', 'CE2', 'QIB', '**************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CE2', 'QIB', '**************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf1', 'QICM01_CP', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf1', 'QICM01_CP', 'CE2', 'QIB', '**************', 1000, '2025-04-01', '2025-04-10', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf1', 'QICM01', 'CE2', 'QIB', '**************', 1000, '2025-04-11', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 39, '2025-04-01', '2025-04-30', 39, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 1, '2025-04-01', '2025-04-30', 0, 1, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf1', 'QICM01', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf1', 'QICM01', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Hf5', 'Pro', 'CE2', 'QIB', '**************', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Hf5', 'Pro', 'CE2', 'QIB', '**************', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CE2', 'QIB', '**************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Hf5', 'Pro', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf1', 'QICM01', 'CE2', 'QIB', '**************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Hf5', 'Pro', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-21', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'Pro', 'CE2', 'QIB', '**************', 40, '2025-04-22', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MAVERICK', 'Pro', 'CE2', 'QIB', '**************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CE2', 'QIB', '**************', 40, '2025-04-01', '2025-04-21', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,FPPL01,ATC01,ARIA01', 'CE2', 'QIB', '**************', 40, '2025-04-22', '2025-04-24', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01, SBE01, STTL01, INFI01, PPB01, MAHA01, BHA001, EIPL01, TIV001, SPB001, HSG001, SAM001, BHARAT01, ORIC01, METRO01, ATC01, SUBH001, MTSL01, KVEE01, SHUB001, SYLI01, SVS01, JYOTI01, BTVL01, SSPV001, ARIA01', 'CE2', 'QIB', '**************', 40, '2025-04-25', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_Atled_NK', 'Pro', 'CI4', 'QIB', '************', 200, '2025-04-01', '2025-04-22', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************', 200, '2025-04-23', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf8', 'Pro', 'CI4', 'QIB', '************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf8', 'Pro', 'CI4', 'QIB', '************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10', 'Pro', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_Atled_NK', 'Pro', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-22', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************', 40, '2025-04-23', '2025-04-29', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF10_Akhil', 'Pro', 'CI4', 'QIB', '************', 40, '2025-04-30', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10(settlement)', 'Pro', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10(settlement)', 'Pro', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF1', 'QICM01', 'CI4', 'QIB', '************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF1', 'QICM01', 'CI4', 'QIB', '*************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf8', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF55', 'Pro', 'CI4', 'QIB', '*************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_Atled_NK', 'Pro', 'CI4', 'QIB', '*************', 200, '2025-04-01', '2025-04-22', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 200, '2025-04-23', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF55', 'Pro', 'CI4', 'QIB', '*************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-23', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-24', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf12', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-24', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-25', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-23', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-24', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('LFT', 'QICM01,WWLLP,INFI01', 'CI4', 'QIB', '************0', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CI4', 'QIB', '************1', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************2', 39, '2025-04-01', '2025-04-30', 39, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************2', 1, '2025-04-01', '2025-04-30', 0, 1, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CI4', 'QIB', '************3', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'CI4', 'QIB', '************4', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CI4', 'QIB', '************5', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10(settlement)', 'Pro', 'CI4', 'QIB', '************6', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************7', 39, '2025-04-01', '2025-04-30', 0, 39, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************7', 1, '2025-04-01', '2025-04-30', 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_Atled_NK', 'Pro', 'CI4', 'QIB', '************8', 40, '2025-04-01', '2025-04-22', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '************8', 40, '2025-04-23', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CI4', 'QIB', '************9', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_Atled_NK', 'Pro', 'CI4', 'QIB', '*************', 200, '2025-04-01', '2025-04-22', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 200, '2025-04-23', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CI4', 'QIB', '*************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF55', 'Pro', 'CI4', 'QIB', '************2', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF55', 'Pro', 'CI4', 'QIB', '*************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_Atled_NK', 'Pro', 'CI4', 'QIB', '*************', 200, '2025-04-01', '2025-04-22', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 200, '2025-04-23', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf10(settlement)', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CI4', 'QIB', '*************', 200, '2025-04-01', '2025-04-23', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 200, '2025-04-24', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'pro', 'CI4', 'QIB', '*************', 200, '2025-04-01', '2025-04-23', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'pro', 'CI4', 'QIB', '*************', 200, '2025-04-24', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Magmio Testing', '', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Greeks soft', 'QICM01_CP/Pro', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf8', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf2_pcp', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-24', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-25', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'CI4', 'QIB', '************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'Pro', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('BULLBEAR', 'Pro', 'CI4', 'QIB', '************', 100, '2025-04-01', '2025-04-30', 100, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf3', 'QICM01_CP', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2', 'FPPL01,SUBH001,EIPL01', 'CI4', 'QIB', '*************', 200, '2025-04-01', '2025-04-15', 200, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2', 'FPPL01,SUBH001,EIPL01,SHUB001', 'CI4', 'QIB', '*************', 200, '2025-04-16', '2025-04-24', 200, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2', 'SUBH001,EIPL01,SHUB001', 'CI4', 'QIB', '*************', 200, '2025-04-25', '2025-04-30', 200, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'HUSH01', 'CI4', 'QIB', '************3', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'HUSH01', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'HUSH01', 'CI4', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CI4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF28', 'Pro', 'BB11', 'QIB', '*************', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-04-01', '2025-04-06', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,FPPL01,ATC01', 'BB11', 'QIB', '*************', 40, '2025-04-07', '2025-04-22', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'BB11', 'QIB', '*************', 40, '2025-04-23', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'BB11', 'QIB', '*************', 39, '2025-04-01', '2025-04-30', 39, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'BB11', 'QIB', '*************', 1, '2025-04-01', '2025-04-30', 0, 1, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General(120 fpga testing)', 'QICM01_CP', 'BB11', 'QIB', '***********', 39, '2025-04-01', '2025-04-30', 39, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'BB11', 'QIB', '***********', 1, '2025-04-01', '2025-04-30', 0, 1, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'BB11', 'QIB', '***********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ARS01, COSHU01,PPB01,SBE01,MAHA01,BHA001, QICM01_CP, TIV001, SAL01,JYOTI01', 'CK4', 'QIB', '************', 1000, '2025-04-01', '2025-04-02', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,SBE01,MAHA01,BHA001, QICM01_CP, TIV001, SAL01,JYOTI01', 'CK4', 'QIB', '************', 1000, '2025-04-03', '2025-04-06', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,SBE01,MAHA01,BHA001, QICM01_CP, TIV001, SAL01,JYOTI01,ATC01', 'CK4', 'QIB', '************', 1000, '2025-04-07', '2025-04-22', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,SBE01,MAHA01,BHA001, QICM01_CP, TIV001, SAL01,JYOTI01,ATC01, BHARAT01, ORIC01, SUBH001, SSMC01, VPB001, MTSL01, SSPV001, ARIA01', 'CK4', 'QIB', '************', 1000, '2025-04-23', '2025-04-24', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,SBE01,MAHA01,BHA001, TIV001, SAL01,JYOTI01,ATC01', 'CK4', 'QIB', '************', 1000, '2025-04-25', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001', 'CK4', 'QIB', '***********1', 1000, '2025-04-01', '2025-04-20', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001,ARIA01', 'CK4', 'QIB', '***********1', 1000, '2025-04-21', '2025-04-22', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001,ARIA01,SSS01,MNI01,SYLI01,RAH01,STTL01,ARV001, NITA001, KIRIT001, SPB001, SVS01, HSG001, SAM001, METRO01, AMPL01, KVEE01, COSHU01, PPB01, SBE01, MAHA01, BHA001, TIV001, SAL01_THETA, JYOTI01, ATC01, INFI01,FFPL01', 'CK4', 'QIB', '***********1', 1000, '2025-04-23', '2025-04-24', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001,ARIA01', 'CK4', 'QIB', '***********1', 1000, '2025-04-25', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01,MNI01,SYLI01,RAH01,STTL01,ARV001,NITA001', 'CK4', 'QIB', '***********2', 1000, '2025-04-01', '2025-04-24', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01,SYLI01,RAH01,STTL01,ARV001,NITA001', 'CK4', 'QIB', '***********2', 1000, '2025-04-25', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KIRIT001,SPB001,SVS01,HSG001,SAM001,FPPL01,METRO01,AMPL01,KVEE01', 'CK4', 'QIB', '***********3', 1000, '2025-04-01', '2025-04-22', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KIRIT001,SPB001,SVS01,HSG001,SAM001,FPPL01,METRO01,AMPL01,KVEE01, FFPL01', 'CK4', 'QIB', '***********3', 1000, '2025-04-23', '2025-04-24', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KIRIT001,SPB001,SVS01,HSG001,SAM001,METRO01,AMPL01,KVEE01', 'CK4', 'QIB', '***********3', 1000, '2025-04-25', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '***********4', 1, '2025-04-01', '2025-04-30', 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf16', 'Pro', 'CK4', 'QIB', '***********4', 39, '2025-04-01', '2025-04-30', 0, 39, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '***********5', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MIPL01', 'CK4', 'QIB', '***********6', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MIPL01', 'CK4', 'QIB', '***********7', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'SSS01', 'CK4', 'QIB', '***********8', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********9', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '***********0', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MODERN', 'CK4', 'QIB', '***********1', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********2', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********3', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KPPL01', 'CK4', 'QIB', '***********4', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KPPL01', 'CK4', 'QIB', '***********5', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MODERN', 'CK4', 'QIB', '***********6', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MODERN', 'CK4', 'QIB', '***********7', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'SSS01', 'CK4', 'QIB', '***********8', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('EXECUTOR', 'QICM01_CP/QICM01', 'CK4', 'QIB', '***********9', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01', 'CK4', 'QIB', '***********0', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_PRO', 'Pro', 'CK4', 'QIB', '***********1', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '***********2', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_PRO', 'Pro', 'CK4', 'QIB', '***********3', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '***********4', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '***********5', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '***********6', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '***********7', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'CRIMSON', 'CK4', 'QIB', '***********8', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'HUSH01', 'CK4', 'QIB', '***********9', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'HUSH01', 'CK4', 'QIB', '***********0', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01,MNI01,SYLI01,RAH01,STTL01,ARV001,NITA001', 'CK4', 'QIB', '***********1', 40, '2025-04-01', '2025-04-24', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'SSS01,SYLI01,RAH01,STTL01,ARV001,NITA001', 'CK4', 'QIB', '***********1', 40, '2025-04-25', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ARS01, COSHU01,PPB01,QICM01,SBE01,MAHA01,BHA001,TIV001, SAL01,JYOTI01', 'CK4', 'QIB', '***********2', 40, '2025-04-01', '2025-04-02', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,QICM01,SBE01,MAHA01,BHA001,TIV001, SAL01,JYOTI01', 'CK4', 'QIB', '***********2', 40, '2025-04-03', '2025-04-06', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'COSHU01,PPB01,QICM01,SBE01,MAHA01,BHA001,TIV001, SAL01,JYOTI01,ATC01', 'CK4', 'QIB', '***********2', 40, '2025-04-07', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001', 'CK4', 'QIB', '***********3', 40, '2025-04-01', '2025-04-20', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001,ARIA01', 'CK4', 'QIB', '***********3', 40, '2025-04-21', '2025-04-22', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'BHARAT01,ORIC01,SUBH001,SSMC01,VPB001,MTSL01,SSPV001,ARIA01,FFPL01', 'CK4', 'QIB', '***********3', 40, '2025-04-23', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MODERN', 'CK4', 'QIB', '***********4', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KIRIT001,SPB001,SVS01,HSG001,SAM001,FPPL01,METRO01,AMPL01,KVEE01', 'CK4', 'QIB', '***********5', 40, '2025-04-01', '2025-04-24', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KIRIT001,SPB001,SVS01,HSG001,SAM001,METRO01,AMPL01,KVEE01', 'CK4', 'QIB', '***********5', 40, '2025-04-25', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('EXECUTOR', 'Pro', 'CK4', 'QIB', '***********6', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'HUSH01', 'CK4', 'QIB', '***********7', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'CRIMSON', 'CK4', 'QIB', '***********8', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'CRIMSON', 'CK4', 'QIB', '***********9', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'CRIMSON', 'CK4', 'QIB', '***********0', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_PRO', 'Pro', 'CK4', 'QIB', '***********1', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_PRO', 'Pro', 'CK4', 'QIB', '***********2', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MIPL01', 'CK4', 'QIB', '***********3', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'MIPL01', 'CK4', 'QIB', '***********4', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********5', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'HUSH01', 'CK4', 'QIB', '***********6', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KPPL01', 'CK4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'KPPL01', 'CK4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********9', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '***********0', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'ART01', 'CK4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'INFI01', 'CK4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('MIDF_THETA', 'INFI01', 'CK4', 'QIB', '************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '***********8', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK4', 'QIB', '************', 40, '2025-04-01', '2025-04-07', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF16', 'Pro', 'CK4', 'QIB', '************', 100, '2025-04-08', '2025-04-30', 0, 100, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_atled_bv', 'Pro', 'CK3', 'QIB', '**********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '**********', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001', 'CK3', 'QIB', '**********', 40, '2025-04-01', '2025-04-01', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001,FPPL01', 'CK3', 'QIB', '**********', 40, '2025-04-02', '2025-04-06', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001,FPPL01,ATC01', 'CK3', 'QIB', '**********', 40, '2025-04-07', '2025-04-20', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001,FPPL01,ATC01,ARIA01', 'CK3', 'QIB', '**********', 40, '2025-04-21', '2025-04-24', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01, SBE01, STTL01, INFI01, PPB01, MAHA01, BHA001, EIPL01, TIV001, SPB001, HSG001, SAM001, BHARAT01, ORIC01, METRO01, ATC01, SUBH001, MTSL01, KVEE01, SHUB001, SYLI01, SVS01, JYOTI01, BTVL01, SSPV001, ARIA01', 'CK3', 'QIB', '**********', 40, '2025-04-25', '2025-04-28', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CK3', 'QIB', '**********', 40, '2025-04-29', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf19', 'QICM01', 'CK3', 'QIB', '**********', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '**********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf23', 'Pro', 'CK3', 'QIB', '**********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '***********', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf23', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001', 'CK3', 'QIB', '***********', 40, '2025-04-01', '2025-04-01', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001,FPPL01', 'CK3', 'QIB', '***********', 40, '2025-04-02', '2025-04-06', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001,FPPL01,ATC01', 'CK3', 'QIB', '***********', 40, '2025-04-07', '2025-04-20', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001,FPPL01,ATC01,ARIA01', 'CK3', 'QIB', '***********', 40, '2025-04-21', '2025-04-25', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01, SBE01, STTL01, INFI01, PPB01, MAHA01, BHA001, EIPL01, TIV001, SPB001, HSG001, SAM001, BHARAT01, ORIC01, METRO01, ATC01, SUBH001, MTSL01, KVEE01, SHUB001, SYLI01, SVS01, JYOTI01, BTVL01, SSPV001, ARIA01', 'CK3', 'QIB', '***********', 40, '2025-04-25', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '***********', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '***********', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '***********', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001', 'CK3', 'QIB', '***********', 40, '2025-04-01', '2025-04-01', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001,FPPL01', 'CK3', 'QIB', '***********', 40, '2025-04-02', '2025-04-06', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001,FPPL01,ATC01', 'CK3', 'QIB', '***********', 40, '2025-04-07', '2025-04-20', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,JYOTI01,SSPV001,FPPL01,ATC01,ARIA01,INFI01', 'CK3', 'QIB', '***********', 40, '2025-04-21', '2025-04-24', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01, SBE01, STTL01, INFI01, PPB01, MAHA01, BHA001, EIPL01, TIV001, SPB001, HSG001, SAM001, BHARAT01, ORIC01, METRO01, ATC01, SUBH001, MTSL01, KVEE01, SHUB001, SYLI01, SVS01, JYOTI01, BTVL01, SSPV001, ARIA01', 'CK3', 'QIB', '***********', 40, '2025-04-25', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CK3', 'QIB', '**********0', 40, '2025-04-01', '2025-04-23', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '**********0', 40, '2025-04-24', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CK3', 'QIB', '**********1', 40, '2025-04-01', '2025-04-23', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '**********1', 40, '2025-04-24', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CK3', 'QIB', '**********2', 200, '2025-04-01', '2025-04-23', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '**********2', 200, '2025-04-24', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CK3', 'QIB', '**********3', 200, '2025-04-01', '2025-04-23', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '**********3', 200, '2025-04-24', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('SEVEN ALPHA', 'SVP01', 'CK3', 'QIB', '**********4', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CK3', 'QIB', '**********5', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CK3', 'QIB', '**********6', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('SEVEN ALPHA', 'SVP', 'CK3', 'QIB', '**********7', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '**********8', 200, '2025-04-01', '2025-04-30', 0, 200, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CK3', 'QIB', '**********9', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_ATLED_AK', 'QICM01', 'CK3', 'QIB', '**********0', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CK3', 'QIB', '**********1', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CK3', 'QIB', '**********2', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********3', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********4', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********5', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********6', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '**********7', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'HUSH01', 'CK3', 'QIB', '**********8', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf23', 'Pro', 'CK3', 'QIB', '**********9', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf23', 'Pro', 'CK3', 'QIB', '**********0', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-04-03', '2025-04-15', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CK3', 'QIB', '***********', 40, '2025-04-16', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CK3', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CK3', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '***********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CK3', 'QIB', '***********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CK3', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CK3', 'QIB', '*************', 40, '2025-04-03', '2025-04-15', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CK3', 'QIB', '*************', 40, '2025-04-16', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 20, '2025-04-01', '2025-04-30', 20, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 20, '2025-04-01', '2025-04-30', 0, 20, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'QICM01_CP', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-20', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-21', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf25', 'AAN001', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF7', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-23', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-24', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-23', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-24', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-04-01', '2025-04-23', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-04-24', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_az', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-04-01', '2025-04-23', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'CM2', 'QIB', '*************', 400, '2025-04-24', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('Hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf5', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'SSS01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-03', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-04', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01_CP', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-03', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-04', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'QICM01_CP', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-21', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-22', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-02', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-03', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-02', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-03', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01', 'CM2', 'QIB', '*************', 400, '2025-04-01', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '***********', 40, '2025-04-03', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '***********', 40, '2025-04-01', '2025-04-21', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'CM2', 'QIB', '***********', 100, '2025-04-22', '2025-04-30', 100, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '***********', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'QICM01_CP', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-06', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'CM2', 'QIB', '*************', 40, '2025-04-07', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'CM2', 'QIB', '*************', 40, '2025-04-01', '2025-04-02', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF4', 'QICM01', 'CM2', 'QIB', '*************', 40, '2025-04-03', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DE5', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DE5', 'QIB', '*************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DE5', 'QIB', '************', 40, '2025-04-02', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DE5', 'QIB', '**************', 40, '2025-04-02', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DE5', 'QIB', '************', 40, '2025-04-02', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DE5', 'QIB', '**************', 40, '2025-04-02', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF24', 'Pro', 'DA6', 'QIB', '************', 1000, '2025-04-01', '2025-04-30', 1000, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-16', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 200, '2025-04-17', '2025-04-23', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 400, '2025-04-24', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-16', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 200, '2025-04-17', '2025-04-23', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 400, '2025-04-24', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-16', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 200, '2025-04-17', '2025-04-23', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 400, '2025-04-24', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_ak', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-21', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_ak', 'Pro', 'DA6', 'QIB', '************', 1000, '2025-04-22', '2025-04-30', 0, 1000, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf6_ak', 'Pro', 'DA6', 'QIB', '************', 1000, '2025-04-01', '2025-04-30', 0, 1000, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF14', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'SAM001, SYLI01', 'DA6', 'QIB', '************', 200, '2025-04-01', '2025-04-01', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'SAM001, SYLI01,METRO01,ORIC01, SBE01, SHUB001, AMPL01, SUBH001, KVEE01, STTL01, ARV001, MTSL01, BHARAT01, SVS01, SSS01, EIPL01, FPPL01', 'DA6', 'QIB', '************', 400, '2025-04-02', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'QICM01_CP', 'DA6', 'QIB', '************', 200, '2025-04-01', '2025-04-01', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'QICM01_CP', 'DA6', 'QIB', '************', 400, '2025-04-02', '2025-04-02', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'METRO01, ORIC01, SBE01, SHUB001, AMPL01, SUBH001, KVEE01, STTL01, ARV001, MTSL01, BHARAT01, SVS01, SAM001, SYLI01,  SSS01, EIPL01, FPPL01', 'DA6', 'QIB', '************', 400, '2025-04-03', '2025-04-30', 400, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-21', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-22', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF24', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf14', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_ATLED_AK', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '************', 1, '2025-04-01', '2025-04-30', 1, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF6_ALTED_BV', 'Pro', 'DA6', 'QIB', '************', 399, '2025-04-01', '2025-04-30', 0, 399, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '************', 400, '2025-04-01', '2025-04-30', 0, 400, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '************', 400, '2025-04-01', '2025-04-30', 0, 400, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('hf11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF5_D_PRO', 'Pro', 'DA6', 'QIB', '************', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF5_D_PRO', 'Pro', 'DA6', 'QIB', '************', 200, '2025-04-01', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF5_D_PRO', 'Pro', 'DA6', 'QIB', '************', 100, '2025-04-01', '2025-04-30', 100, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 0, 40, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF19', 'QICM01_CP', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', '', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-23', 40, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21', 'Pro', 'DA6', 'QIB', '************', 200, '2025-04-24', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-03', '2025-04-15', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-16', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '**************', 40, '2025-04-01', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'Pro', 'DA6', 'QIB', '**************', 40, '2025-04-03', '2025-04-15', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '**************', 40, '2025-04-16', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '************', 40, '2025-04-01', '2025-04-21', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF11', 'Pro', 'DA6', 'QIB', '************', 100, '2025-04-22', '2025-04-30', 100, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001', 'DA6', 'QIB', '**************', 40, '2025-04-01', '2025-04-01', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,FPPL01', 'DA6', 'QIB', '**************', 40, '2025-04-02', '2025-04-06', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,FPPL01,ATC01', 'DA6', 'QIB', '**************', 40, '2025-04-07', '2025-04-20', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS,SBE01,STTL01,MNI01,PPB01,MAHA01,BHA001,TIV001,SPB001,HSG001,BHARAT01,EIPL01,ORIC01,METRO01,SAM001,AMPL01,SUBH001,MTSL01,KVEE01,SHUB001,SYLI01,SVS01,JYOTI01,SSPV001,FPPL01,ATC01,ARIA01', 'DA6', 'QIB', '**************', 40, '2025-04-21', '2025-04-24', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF2_PCP', 'SSS01, SBE01, STTL01, INFI01, PPB01, MAHA01, BHA001, EIPL01, TIV001, SPB001, HSG001, SAM001, BHARAT01, ORIC01, METRO01, ATC01, SUBH001, MTSL01, KVEE01, SHUB001, SYLI01, SVS01, JYOTI01, BTVL01, SSPV001, ARIA01', 'DA6', 'QIB', '**************', 40, '2025-04-25', '2025-04-30', 40, 0, 'Direct');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'ART01', 'DA6', 'QIB', '************', 200, '2025-04-01', '2025-04-02', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21_ART01', 'ART01', 'DA6', 'QIB', '************', 200, '2025-04-03', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'CRIMSON', 'DA6', 'QIB', '************', 200, '2025-04-01', '2025-04-02', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('HF21_CRIMSON', 'CRIMSON', 'DA6', 'QIB', '************', 200, '2025-04-03', '2025-04-30', 200, 0, 'Switch');
INSERT INTO TraderSessions (Trader_ID, Client_ID, Rack_ID, Firm_Name, Tap_IP, ops_per_second, Start_Date, END_DATE, FO_OPS, CM_OPS, LinkType) VALUES ('General', 'MODERN', 'DA6', 'QIB', '************', 200, '2025-04-01', '2025-04-02', 200, 0, 'Switch');
