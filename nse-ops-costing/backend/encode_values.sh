#!/bin/bash

# JWT Configuration
echo "JWT_SECRET: eW91ci1zZWNyZXQta2V5"

# Redis Configuration
echo "REDIS_PASSWORD: RkNmeFJLbUZETg=="

# Database Configuration
echo "DB_HOST: bXlzcWwtc2VydmljZQ=="
echo "DB_PORT: MzMwNg=="
echo "DB_USER: cm9vdA=="
echo "DB_PASSWORD: c3Ryb25nX3Bhc3N3b3Jk"
echo "DB_NAME: bnNlX2Nvc3Rpbmc="

# Email Configuration
echo "SMTP_HOST: c210cC5nbWFpbC5jb20="
echo "SMTP_PORT: NTg3"
echo "SMTP_USER: ************************************"
echo "SMTP_PASS: bmJ0eCBmYWh6IHZjc3YgdWxmeA=="
echo "EMAIL_FROM: ************************************"

# ELK Stack Configuration
echo "ELASTICSEARCH_USERNAME: ZWxhc3RpYw=="
echo "ELASTICSEARCH_PASSWORD: dXlRQnA4djl4aEo0VE00NVU5OTMwcnY5"
