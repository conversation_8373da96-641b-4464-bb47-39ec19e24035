# NSE Operations Costing Backend

This repository contains the backend service for the NSE Operations Costing application. The service is built with Node.js and Express, and uses MySQL for data storage.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Local Development](#local-development)
- [CI/CD Pipeline](#cicd-pipeline)
- [Deployment](#deployment)
- [API Documentation](#api-documentation)
- [Environment Variables](#environment-variables)

## Architecture Overview

The NSE Operations Costing backend is a Node.js application built with Express. It provides RESTful APIs for managing trader sessions, calculating costs, and user authentication.

### Key Components

- **Express Server**: Handles HTTP requests and routes
- **MySQL Database**: Stores trader sessions, user data, and operational metrics
- **Redis**: Used for caching and storing JWT tokens
- **Elasticsearch**: Used for logging and monitoring
- **JWT Authentication**: Secures API endpoints

### Directory Structure

```
backend/
├── src/
│   ├── logs/                  # Log files
│   └── server/
│       ├── config/            # Configuration files
│       ├── middleware/        # Express middleware
│       ├── routes/            # API routes
│       └── utils/             # Utility functions
├── server.js                  # Main application entry point
├── package.json               # Node.js dependencies
├── Dockerfile                 # Container definition
└── deployment.yaml            # Kubernetes deployment manifest
```

## Local Development

### Prerequisites

- Node.js 18 or higher
- MySQL 8.0 or higher
- Redis (optional for local development)

### Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file based on the environment variables listed in the [Environment Variables](#environment-variables) section.

4. Build the application:
   ```bash
   npm run build
   ```

5. Start the server:
   ```bash
   npm start
   ```

### Testing

The application includes comprehensive unit and integration tests using Jest and Supertest.

1. Run all tests with coverage:
   ```bash
   npm test
   ```

2. Run only unit tests:
   ```bash
   npm run test:unit
   ```

3. Run only integration tests:
   ```bash
   npm run test:integration
   ```

4. Run tests in watch mode during development:
   ```bash
   npm run test:watch
   ```

Test coverage reports are generated in the `coverage` directory.

## CI/CD Pipeline

This project uses Bitbucket Pipelines for continuous integration and deployment. The pipeline is defined in `bitbucket-pipelines.yml` and is configured to run only when manually triggered. It includes the following stages:

1. **Build**: Installs dependencies
2. **Test**: Runs unit and integration tests with coverage reporting
3. **Security Scan**: Uses Trivy to scan for vulnerabilities
4. **SonarQube Analysis**: Analyzes code quality and test coverage
5. **Build and Push Image**: Builds the Docker image using Buildpacks and pushes it to AWS ECR
6. **Generate K8s Manifests**: Generates Kubernetes manifests for deployment
7. **Update ArgoCD Manifests**: Updates the Kubernetes manifests in a separate repository for ArgoCD deployment

### Pipeline Variables

The following variables need to be set in Bitbucket repository settings:

- `AWS_ACCESS_KEY_ID`: AWS access key for ECR access
- `AWS_SECRET_ACCESS_KEY`: AWS secret key for ECR access
- `AWS_DEFAULT_REGION`: AWS region for ECR (e.g., us-east-1)
- `ECR_REPOSITORY_URL`: URL of the shared ECR repository (e.g., 123456789012.dkr.ecr.us-east-1.amazonaws.com/product)
- `SONAR_HOST_URL`: URL of the SonarQube server
- `SONAR_TOKEN`: Token for SonarQube authentication
- Various base64 encoded environment variables for the application (see [base64-encoded-values.md](base64-encoded-values.md) for reference)

### Manual Pipeline Triggers

The pipeline is configured with two custom pipelines that can be triggered manually:

1. **deploy-to-dev**: For deploying to the development environment
2. **deploy-to-prod**: For deploying to the production environment

To trigger a pipeline manually, go to Bitbucket Pipelines and click "Run pipeline".

## Deployment

The application is deployed to a self-hosted Kubernetes cluster using ArgoCD with the manifests generated in the CI/CD pipeline.

### Kubernetes Resources

- **Deployment**: Manages the application pods
- **Service**: Exposes the application within the cluster
- **HorizontalPodAutoscaler**: Automatically scales the application based on CPU and memory usage
- **ConfigMap**: Stores non-sensitive configuration
- **Secret**: Stores sensitive configuration

### ArgoCD Integration

ArgoCD is used for continuous delivery to the Kubernetes cluster. The pipeline updates a separate Kubernetes manifests repository that ArgoCD monitors.

The workflow is as follows:

1. The pipeline builds and tests the application
2. It creates a Docker image and pushes it to ECR
3. It generates a combined Kubernetes manifest file with all resources (ConfigMap, Secret, Deployment, Service, HPA)
4. It updates the `nse-ops-costing-backend.yaml` file in the kubernetes-manifests repository with the combined manifest
5. ArgoCD detects the changes and deploys all the resources

To set up ArgoCD:

1. Install ArgoCD on your Kubernetes cluster
2. Create an ArgoCD application that points to the kubernetes-manifests repository
3. Configure ArgoCD to automatically sync changes
4. Ensure that both repositories are in the same Bitbucket workspace for automatic authentication

For detailed instructions, see the [PIPELINE.md](PIPELINE.md) file.

### Autoscaling

The application is configured to automatically scale based on CPU and memory usage:

- Minimum replicas: 2
- Maximum replicas: 10
- CPU target utilization: 70%
- Memory target utilization: 80%

## API Documentation

### Authentication Endpoints

- `POST /api/auth/login`: Authenticate a user and get JWT tokens
- `POST /api/auth/logout`: Invalidate a JWT token
- `POST /api/auth/refresh-token`: Get a new access token using a refresh token
- `POST /api/auth/request-password-reset`: Request a password reset email
- `POST /api/auth/reset-password`: Reset a user's password
- `GET /api/auth/health`: Health check endpoint

### Data Endpoints

- `GET /api/get-ids`: Get all trader IDs
- `GET /api/get-session-ids/:traderId`: Get session IDs for a trader
- `GET /api/get-rack-ids/:traderId/:sessionId`: Get rack IDs for a trader and session
- `GET /api/get-firm-names/:traderId/:sessionId/:rackId`: Get firm names for a trader, session, and rack
- `GET /api/get-tap-ips/:traderId/:sessionId/:rackId/:firmName`: Get tap IPs for a specific combination
- `POST /api/submit-form`: Submit a new trader session
- `GET /api/fetch-sessions`: Fetch trader sessions with filters
- `POST /api/update-session`: Update an existing trader session
- `POST /api/calculate_daily_sessions`: Calculate daily sessions for a date range
- `GET /api/fetch_ops_price`: Fetch operations price for a date range
- `POST /api/delete-session`: Delete a trader session

## Environment Variables

The application uses the following environment variables:

### Server Configuration
- `PORT`: Server port (default: 3001)
- `NODE_ENV`: Environment (development, production)

### JWT Configuration
- `JWT_SECRET`: Secret key for JWT signing
- `JWT_EXPIRATION`: JWT expiration time
- `JWT_REFRESH_EXPIRATION`: Refresh token expiration time

### Redis Configuration
- `REDIS_HOST`: Redis server hostname
- `REDIS_PORT`: Redis server port
- `REDIS_PASSWORD`: Redis server password

### Email Configuration
- `SMTP_HOST`: SMTP server hostname
- `SMTP_PORT`: SMTP server port
- `SMTP_USER`: SMTP username
- `SMTP_PASS`: SMTP password
- `EMAIL_FROM`: Sender email address

### Database Configuration
- `DB_HOST`: MySQL server hostname
- `DB_PORT`: MySQL server port
- `DB_USER`: MySQL username
- `DB_PASSWORD`: MySQL password
- `DB_NAME`: MySQL database name

### Frontend Configuration
- `FRONTEND_URL`: URL of the frontend application

### Elasticsearch Configuration
- `ELASTICSEARCH_HOST`: Elasticsearch hostname
- `ELASTICSEARCH_PORT`: Elasticsearch port
- `ELASTICSEARCH_USERNAME`: Elasticsearch username
- `ELASTICSEARCH_PASSWORD`: Elasticsearch password
