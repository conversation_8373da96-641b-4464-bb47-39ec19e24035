# CI/CD Pipeline Documentation

This document provides detailed information about the CI/CD pipeline for the NSE Operations Costing Backend application.

## Overview

The CI/CD pipeline is implemented using Bitbucket Pipelines and consists of several stages:

1. Build
2. Test (Unit and Integration Tests with Coverage)
3. Security Scan with Trivy
4. SonarQube Analysis (including Code Quality and Test Coverage)
5. Build and Push Image to AWS ECR
6. Generate Kubernetes Manifests
7. Update ArgoCD Manifests in a separate repository

## Pipeline Configuration

The pipeline is defined in the `bitbucket-pipelines.yml` file at the root of the repository. It is configured to run only when manually triggered, not on every push.

### Pipeline Stages

#### 1. Build

This stage installs dependencies:

```yaml
- step: &build
    name: Build
    caches:
      - npm
    script:
      - npm install
    artifacts:
      - node_modules/**
```

#### 2. Test

This stage runs unit and integration tests with coverage reporting:

```yaml
- step: &test
    name: Test
    caches:
      - npm
    script:
      - npm run test
    artifacts:
      - coverage/**
```

#### 3. Security Scan

This stage uses Trivy to scan the Docker image for vulnerabilities:

```yaml
- step: &security-scan
    name: Security Scan
    script:
      - pipe: atlassian/trivy-scan-pipe:1.0.0
        variables:
          IMAGE_NAME: $IMAGE_NAME
          SEVERITY: 'CRITICAL,HIGH'
          EXIT_CODE: '1'
          FORMAT: 'table'
    services:
      - docker
```

#### 4. SonarQube Analysis

This stage analyzes code quality and test coverage using SonarQube:

```yaml
- step: &sonarqube-analysis
    name: SonarQube Analysis
    script:
      - pipe: sonarsource/sonarqube-scan:1.0.0
        variables:
          SONAR_HOST_URL: ${SONAR_HOST_URL}
          SONAR_TOKEN: ${SONAR_TOKEN}
          EXTRA_ARGS: >
            -Dsonar.projectKey=nse-ops-costing-backend
            -Dsonar.projectName="NSE Ops Costing Backend"
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.coverage.exclusions=tests/**,jest.config.js,**/*.test.js
```

#### 5. Build and Push Image

This stage builds the Docker image using Buildpacks and pushes it to AWS ECR:

```yaml
- step: &build-push-image
    name: Build and Push Image
    script:
      - pipe: atlassian/aws-ecr-push-image:1.5.0
        variables:
          AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
          AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
          AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
          IMAGE_NAME: $IMAGE_NAME
          TAGS: '${BITBUCKET_COMMIT}'
          BUILDPACK: 'true'
          BUILDPACK_BUILDER: 'heroku/buildpacks:20'
    services:
      - docker
```

#### 6. Generate Kubernetes Manifests

This stage generates Kubernetes manifests for deployment:

```yaml
- step: &generate-k8s-manifests
    name: Generate K8s Manifests
    script:
      # Source the variables from the previous step
      - source variables.env
      - echo "Using image: $FULL_IMAGE_REFERENCE"
      - mkdir -p k8s-manifests
      - envsubst < combined-manifests.yaml > k8s-manifests/combined-manifests.yaml
    artifacts:
      - k8s-manifests/**
```

The `combined-manifests.yaml` file contains all Kubernetes resources (ConfigMap, Secret, Deployment, Service, and HorizontalPodAutoscaler) separated by `---` delimiters, ensuring that all resources are deployed together.

#### 7. Update ArgoCD Manifests

This stage updates the Kubernetes manifests in a separate repository that ArgoCD monitors:

```yaml
- step: &update-argocd-manifests
    name: Update ArgoCD Manifests
    script:
      # Source the variables from the previous step
      - source variables.env
      - echo "Using image: $FULL_IMAGE_REFERENCE"

      # Configure Git user
      - git config --global user.email "${BITBUCKET_REPO_OWNER}@users.noreply.bitbucket.org"
      - git config --global user.name "${BITBUCKET_REPO_OWNER}"

      # Clone the kubernetes-manifests repository using Bitbucket access token
      # The $BITBUCKET_ACCESS_TOKEN is automatically available in pipelines
      - git clone https://${BITBUCKET_STEP_TRIGGERER_UUID}:${BITBUCKET_ACCESS_TOKEN}@bitbucket.org/macehand/kubernetes-manifests.git

      # Update the manifest file
      - cd kubernetes-manifests
      - git checkout dev
      - cp ../k8s-manifests/combined-manifests.yaml nse-ops-costing-backend.yaml

      # Commit and push changes
      - git add nse-ops-costing-backend.yaml
      - git commit -m "Update nse-ops-costing-backend image to $FULL_IMAGE_REFERENCE [skip ci]"
      - git push origin dev
```

ArgoCD monitors the `kubernetes-manifests` repository and automatically deploys changes to the Kubernetes cluster.

## Manual Pipeline Triggers

The pipeline is configured to run only when manually triggered. There are two custom pipelines available:

1. **deploy-to-dev**: Deploys the application to the development environment
2. **deploy-to-prod**: Deploys the application to the production environment

To trigger a pipeline manually:

1. Go to your Bitbucket repository
2. Navigate to Pipelines
3. Click on "Run pipeline"
4. Select the pipeline you want to run (deploy-to-dev or deploy-to-prod)
5. Click "Run"

This approach prevents the pipeline from running on every push, giving you more control over when deployments happen.

## Pipeline Variables

The following variables need to be set in Bitbucket repository settings:

### AWS Credentials

- `AWS_ACCESS_KEY_ID`: AWS access key for ECR access
- `AWS_SECRET_ACCESS_KEY`: AWS secret key for ECR access
- `AWS_DEFAULT_REGION`: AWS region for ECR (e.g., us-east-1)
- `ECR_REPOSITORY_URL`: URL of the shared ECR repository (e.g., 123456789012.dkr.ecr.us-east-1.amazonaws.com/product)

### Git Access

No additional configuration is needed for Git access. The pipeline uses Bitbucket's built-in authentication to access the kubernetes-manifests repository.

### SonarQube Credentials

- `SONAR_HOST_URL`: URL of the SonarQube server
- `SONAR_TOKEN`: Token for SonarQube authentication

### Docker Image

The Docker image name is automatically set to `nse-ops-costing-backend` in the pipeline script. The full image reference will be:

```
<ECR_REPOSITORY_URL>/nse-ops-costing-backend:<BITBUCKET_COMMIT>
```

For example: `123456789012.dkr.ecr.us-east-1.amazonaws.com/product/nse-ops-costing-backend:a1b2c3d4`

### Application Secrets (Base64 Encoded)

The following variables need to be set in your Bitbucket repository settings. These values should be base64 encoded. A reference file `base64-encoded-values.md` is provided with example values.

- `JWT_SECRET_BASE64`: Secret key for JWT signing
- `REDIS_PASSWORD_BASE64`: Redis server password
- `DB_HOST_BASE64`: MySQL server hostname
- `DB_PORT_BASE64`: MySQL server port
- `DB_USER_BASE64`: MySQL username
- `DB_PASSWORD_BASE64`: MySQL password
- `DB_NAME_BASE64`: MySQL database name
- `SMTP_HOST_BASE64`: SMTP server hostname
- `SMTP_PORT_BASE64`: SMTP server port
- `SMTP_USER_BASE64`: SMTP username
- `SMTP_PASS_BASE64`: SMTP password
- `EMAIL_FROM_BASE64`: Sender email address
- `ELASTICSEARCH_USERNAME_BASE64`: Elasticsearch username
- `ELASTICSEARCH_PASSWORD_BASE64`: Elasticsearch password

To generate base64 encoded values, you can use the following command:

```bash
echo -n 'your-value' | base64
```

## Buildpacks

The pipeline uses Cloud Native Buildpacks to build the Docker image instead of a traditional Dockerfile. Buildpacks automatically detect the application type and build a container image without requiring a Dockerfile.

Benefits of using Buildpacks:

1. **Simplified Build Process**: No need to maintain a Dockerfile
2. **Optimized Images**: Buildpacks create optimized images with best practices
3. **Security**: Buildpacks include security patches and updates
4. **Consistency**: Ensures consistent builds across environments

## AWS ECR Integration

The pipeline pushes the Docker image to Amazon Elastic Container Registry (ECR). To set up ECR:

1. Create an ECR repository in your AWS account
2. Create an IAM user with permissions to push to ECR
3. Set the AWS credentials in Bitbucket repository settings

## Kubernetes Deployment

The pipeline generates Kubernetes manifests and deploys them to an EKS cluster. The deployment includes:

1. **Deployment**: Manages the application pods
2. **Service**: Exposes the application within the cluster
3. **HorizontalPodAutoscaler**: Automatically scales the application
4. **ConfigMap**: Stores non-sensitive configuration
5. **Secret**: Stores sensitive configuration

## ArgoCD Integration

This pipeline is designed to work with ArgoCD for continuous delivery. The pipeline updates a separate Kubernetes manifests repository that ArgoCD monitors. Here's how it works:

1. **Install ArgoCD** on your self-hosted Kubernetes cluster following the [official documentation](https://argo-cd.readthedocs.io/en/stable/getting_started/).

2. **Create an ArgoCD Application** that points to the kubernetes-manifests repository:
   ```yaml
   apiVersion: argoproj.io/v1alpha1
   kind: Application
   metadata:
     name: nse-ops-costing-backend
     namespace: argocd
   spec:
     project: default
     source:
       repoURL: *****************:macehand/kubernetes-manifests.git
       targetRevision: dev
       path: .
     destination:
       server: https://kubernetes.default.svc
       namespace: nse-ops
     syncPolicy:
       automated:
         prune: true
         selfHeal: true
       syncOptions:
         - CreateNamespace=true
   ```

3. **Configure ArgoCD** to use the appropriate SSH credentials to access your Bitbucket repository.

4. **Repository Access**: Ensure that the Bitbucket Pipeline has access to the kubernetes-manifests repository. This is handled automatically if both repositories are in the same Bitbucket workspace.

5. **Set up Webhook** in the kubernetes-manifests repository to notify ArgoCD when changes are pushed.

With this setup, whenever you push changes to your application repository:
1. The pipeline builds and tests your application
2. It creates a new Docker image and pushes it to ECR
3. It generates updated Kubernetes manifests
4. It clones the kubernetes-manifests repository
5. It updates the nse-ops-costing-backend.yaml file with the new image reference
6. It commits and pushes the changes to the kubernetes-manifests repository
7. ArgoCD detects the changes and automatically deploys the updated manifests to your cluster

## Troubleshooting

### Common Issues

1. **Image Build Failures**:
   - Check that the application is compatible with the buildpack
   - Ensure all dependencies are properly declared in package.json

2. **Deployment Failures**:
   - Verify that the Kubernetes manifests are valid
   - Check that the EKS cluster has the necessary permissions
   - Ensure that the secrets and configmaps are properly defined

3. **Security Scan Failures**:
   - Address the vulnerabilities reported by Trivy
   - Update dependencies to fix security issues

### Logs and Monitoring

- Pipeline logs are available in Bitbucket Pipelines
- Application logs are available in Elasticsearch
- Kubernetes logs can be accessed using kubectl
