apiVersion: apps/v1
kind: Deployment
metadata:
  name: nse-ops-backend
  namespace: default
  labels:
    app: nse-ops-backend
spec:
  replicas: 1  # Adjust as needed
  selector:
    matchLabels:
      app: nse-ops-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:
      labels:
        app: nse-ops-backend
    spec:
      containers:
        - name: nse-ops-backend
          image: abhinav173/cicdimages:latest 
          ports:
            - containerPort: 3001
          imagePullPolicy: IfNotPresent  # Or Always, depending on your needs
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
      imagePullSecrets: # Specify secrets to access the private registry
        - name: my-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: nse-ops-backend-service
spec:
  type: NodePort
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
    nodePort: 30001  # Port to expose on the host machine
  selector:
    app: nse-ops-backend     