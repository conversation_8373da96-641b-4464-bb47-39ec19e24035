const express = require('express');
const request = require('supertest');
const authRoutes = require('../../../src/server/routes/auth');
const bcrypt = require('bcryptjs');
const { MockDBConnection } = require('../../test-helpers');

// Mock dependencies
jest.mock('bcryptjs', () => ({
  compare: jest.fn(),
  hash: jest.fn().mockResolvedValue('hashed_password')
}));

jest.mock('../../../src/server/utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

// Create MockRedisClient before using it in the mock
class MockRedisClient {
  constructor() {
    this.data = {};
  }

  async set(key, value, expiryType, expiryTime) {
    this.data[key] = value;
    return 'OK';
  }

  async get(key) {
    return this.data[key] || null;
  }

  async del(key) {
    delete this.data[key];
    return 1;
  }

  on() {
    return this;
  }
}

jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => {
    return new MockRedisClient();
  });
});

jest.mock('../../../src/server/utils/email', () => ({
  sendPasswordResetEmail: jest.fn().mockResolvedValue(true)
}));

describe('Auth Routes', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());

    // Mock database connection
    app.use((req, res, next) => {
      req.db = new MockDBConnection({
        'SELECT * FROM users WHERE username': [[{
          id: 1,
          username: 'testuser',
          password: 'hashed_password',
          email: '<EMAIL>',
          role: 'admin'
        }], []],
        'SELECT * FROM users WHERE email': [[{
          id: 1,
          username: 'testuser',
          password: 'hashed_password',
          email: '<EMAIL>',
          role: 'admin'
        }], []]
      });
      next();
    });

    app.use('/api/auth', authRoutes);
  });

  describe('POST /api/auth/login', () => {
    test('should return 400 if username or password is missing', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('message', 'Username and password are required');
    });

    test('should return 401 if user does not exist', async () => {
      // Override the mock DB to return empty result
      app.use((req, res, next) => {
        req.db = new MockDBConnection({
          'SELECT * FROM users WHERE username': [[], []]
        });
        next();
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send({ username: 'nonexistent', password: 'password' });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('message', 'Invalid credentials');
    });

    test('should return 401 if password is incorrect', async () => {
      // Mock bcrypt.compare to return false
      bcrypt.compare.mockResolvedValueOnce(false);

      const response = await request(app)
        .post('/api/auth/login')
        .send({ username: 'testuser', password: 'wrong_password' });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('message', 'Invalid credentials');
    });

    test('should return tokens and user info if login is successful', async () => {
      // Mock bcrypt.compare to return true
      bcrypt.compare.mockResolvedValueOnce(true);

      const response = await request(app)
        .post('/api/auth/login')
        .send({ username: 'testuser', password: 'correct_password' });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('id', 1);
      expect(response.body.user).toHaveProperty('username', 'testuser');
      expect(response.body.user).toHaveProperty('role', 'admin');
    });
  });

  describe('GET /api/auth/health', () => {
    test('should return 200 OK with status information', async () => {
      const response = await request(app)
        .get('/api/auth/health');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('POST /api/auth/request-password-reset', () => {
    test('should return 400 if email is missing', async () => {
      const response = await request(app)
        .post('/api/auth/request-password-reset')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('message', 'Email is required');
    });

    test('should return success message even if email does not exist (security)', async () => {
      // Override the mock DB to return empty result
      app.use((req, res, next) => {
        req.db = new MockDBConnection({
          'SELECT * FROM users WHERE email': [[], []]
        });
        next();
      });

      const response = await request(app)
        .post('/api/auth/request-password-reset')
        .send({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'If an account exists with this email, you will receive a password reset link.');
    });

    test('should send reset email if email exists', async () => {
      const emailService = require('../../../src/server/utils/email');

      const response = await request(app)
        .post('/api/auth/request-password-reset')
        .send({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'If an account exists with this email, you will receive a password reset link.');
      expect(emailService.sendPasswordResetEmail).toHaveBeenCalled();
    });
  });
});
