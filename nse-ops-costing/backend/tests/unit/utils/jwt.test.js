const jwt = require('jsonwebtoken');
const JWTService = require('../../../src/server/utils/jwt');
const config = require('../../../src/server/config/config_test');

// Create MockRedisClient before using it in the mock
class MockRedisClient {
  constructor() {
    this.data = {};
  }

  async set(key, value, expiryType, expiryTime) {
    this.data[key] = value;
    return 'OK';
  }

  async get(key) {
    return this.data[key] || null;
  }

  async del(key) {
    delete this.data[key];
    return 1;
  }

  on() {
    return this;
  }
}

// Mock Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => {
    return new MockRedisClient();
  });
});

describe('JWT Service', () => {
  let token;
  const user = { id: 1, username: 'testuser', role: 'admin' };

  beforeEach(() => {
    // Generate a test token
    token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      config.JWT_SECRET,
      { expiresIn: '1h' }
    );
  });

  test('should invalidate a token', async () => {
    const result = await JWTService.invalidateToken(token);
    expect(result).toBe(true);
  });

  test('should refresh a token', async () => {
    // Create a refresh token
    const refreshToken = jwt.sign(
      { id: user.id },
      config.JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Mock the Redis get method to return the refresh token
    const redis = new MockRedisClient();
    redis.set(`refresh_token:${user.id}`, refreshToken);
    JWTService.redis = redis;

    // Refresh the token
    const result = await JWTService.refreshToken(refreshToken);

    // Verify the result
    expect(result).toHaveProperty('token');
    expect(result).toHaveProperty('refreshToken');

    // Verify the token payload
    const decoded = jwt.verify(result.token, config.JWT_SECRET);
    expect(decoded).toHaveProperty('id', user.id);
  });

  test('should throw an error for invalid refresh token', async () => {
    const invalidToken = 'invalid-token';

    await expect(JWTService.refreshToken(invalidToken)).rejects.toThrow();
  });
});
