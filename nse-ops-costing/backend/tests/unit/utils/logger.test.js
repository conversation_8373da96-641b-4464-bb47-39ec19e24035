// <PERSON>ck winston before requiring logger
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
};

jest.mock('winston', () => ({
  format: {
    combine: jest.fn(),
    timestamp: jest.fn(),
    printf: jest.fn(),
    colorize: jest.fn(),
    json: jest.fn()
  },
  createLogger: jest.fn().mockReturnValue(mockLogger),
  transports: {
    Console: jest.fn(),
    File: jest.fn()
  }
}));

// Mock winston-daily-rotate-file
jest.mock('winston-daily-rotate-file', () => jest.fn());

// Mock elasticsearch
jest.mock('@elastic/elasticsearch', () => ({
  Client: jest.fn().mockImplementation(() => ({
    ping: jest.fn().mockResolvedValue({ statusCode: 200 }),
    index: jest.fn().mockResolvedValue({ result: 'created' })
  }))
}));

// Now require the logger
const logger = require('../../../src/server/utils/logger');

describe('Logger Utility', () => {
  test('should have all required logging methods', () => {
    expect(logger).toHaveProperty('info');
    expect(logger).toHaveProperty('error');
    expect(logger).toHaveProperty('warn');
    expect(logger).toHaveProperty('debug');
  });

  test('should log info messages', () => {
    const spy = jest.spyOn(logger, 'info');
    logger.info('Test info message');
    expect(spy).toHaveBeenCalledWith('Test info message');
  });

  test('should log error messages', () => {
    const spy = jest.spyOn(logger, 'error');
    logger.error('Test error message');
    expect(spy).toHaveBeenCalledWith('Test error message');
  });

  test('should log warning messages', () => {
    const spy = jest.spyOn(logger, 'warn');
    logger.warn('Test warning message');
    expect(spy).toHaveBeenCalledWith('Test warning message');
  });

  test('should log debug messages', () => {
    const spy = jest.spyOn(logger, 'debug');
    logger.debug('Test debug message');
    expect(spy).toHaveBeenCalledWith('Test debug message');
  });
});
