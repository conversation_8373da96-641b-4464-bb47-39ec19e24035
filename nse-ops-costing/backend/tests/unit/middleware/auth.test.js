// Mock Redis and other dependencies before requiring auth middleware
class MockRedisClient {
  constructor() {
    this.data = {};
  }

  async set(key, value, expiryType, expiryTime) {
    this.data[key] = value;
    return 'OK';
  }

  async get(key) {
    return this.data[key] || null;
  }

  async del(key) {
    delete this.data[key];
    return 1;
  }

  on() {
    return this;
  }
}

jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => {
    return new MockRedisClient();
  });
});

// Mock the logger
jest.mock('../../../src/server/utils/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
}));

// Mock JWT service
jest.mock('../../../src/server/utils/jwt', () => ({
  isTokenBlacklisted: jest.fn().mockResolvedValue(false)
}));

// Now require the auth middleware
const { authMiddleware, roleMiddleware } = require('../../../src/server/middleware/auth');
const { generateTestToken } = require('../../test-helpers');
const jwt = require('jsonwebtoken');

describe('Auth Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      headers: {}
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    next = jest.fn();
  });

  describe('authMiddleware', () => {
    test('should return 401 if no token is provided', () => {
      authMiddleware(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Authentication required' });
      expect(next).not.toHaveBeenCalled();
    });

    test('should return 401 if token format is invalid', () => {
      req.headers.authorization = 'InvalidFormat';

      authMiddleware(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Authentication required' });
      expect(next).not.toHaveBeenCalled();
    });

    test('should return 401 if token is invalid', () => {
      req.headers.authorization = 'Bearer invalid-token';

      authMiddleware(req, res, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Invalid token' });
      expect(next).not.toHaveBeenCalled();
    });

    test('should call next() if token is valid', () => {
      const token = generateTestToken();
      req.headers.authorization = `Bearer ${token}`;

      authMiddleware(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(req.user).toBeDefined();
      expect(req.user.id).toBe(1);
      expect(req.user.username).toBe('testuser');
      expect(req.user.role).toBe('admin');
    });
  });

  describe('roleMiddleware', () => {
    test('should return 403 if user does not have required role', () => {
      const token = generateTestToken({ id: 1, username: 'testuser', role: 'user' });
      req.headers.authorization = `Bearer ${token}`;

      // First apply authMiddleware to set req.user
      authMiddleware(req, res, next);

      // Reset next and res for the roleMiddleware test
      next.mockReset();
      res.status.mockClear();
      res.json.mockClear();

      // Apply roleMiddleware requiring admin role
      const middleware = roleMiddleware(['admin']);
      middleware(req, res, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({ message: 'Insufficient permissions' });
      expect(next).not.toHaveBeenCalled();
    });

    test('should call next() if user has required role', () => {
      const token = generateTestToken({ id: 1, username: 'testuser', role: 'admin' });
      req.headers.authorization = `Bearer ${token}`;

      // First apply authMiddleware to set req.user
      authMiddleware(req, res, next);

      // Reset next for the roleMiddleware test
      next.mockReset();

      // Apply roleMiddleware requiring admin role
      const middleware = roleMiddleware(['admin']);
      middleware(req, res, next);

      expect(next).toHaveBeenCalled();
    });
  });
});
