const jwt = require('jsonwebtoken');
const config = require('../src/server/config/config_test');

/**
 * Generate a valid JWT token for testing
 * @param {Object} user - User object
 * @returns {String} JWT token
 */
function generateTestToken(user = { id: 1, username: 'testuser', role: 'admin' }) {
  return jwt.sign(
    { id: user.id, username: user.username, role: user.role },
    config.JWT_SECRET,
    { expiresIn: '1h' }
  );
}

/**
 * Generate a valid refresh token for testing
 * @param {Object} user - User object
 * @returns {String} Refresh token
 */
function generateTestRefreshToken(user = { id: 1 }) {
  return jwt.sign(
    { id: user.id },
    config.JWT_SECRET,
    { expiresIn: '7d' }
  );
}

/**
 * Mock Redis client for testing
 */
class MockRedisClient {
  constructor() {
    this.data = {};
  }

  async set(key, value, expiryType, expiryTime) {
    this.data[key] = value;
    return 'OK';
  }

  async get(key) {
    return this.data[key] || null;
  }

  async del(key) {
    delete this.data[key];
    return 1;
  }
}

/**
 * Mock MySQL connection for testing
 */
class MockDBConnection {
  constructor(queryResults = {}) {
    this.queryResults = queryResults;
  }

  query(sql, params = []) {
    // Return predefined results based on the SQL query
    const key = Object.keys(this.queryResults).find(k => sql.includes(k));
    if (key) {
      return Promise.resolve(this.queryResults[key]);
    }
    return Promise.resolve([[], []]);
  }
}

module.exports = {
  generateTestToken,
  generateTestRefreshToken,
  MockRedisClient,
  MockDBConnection
};
