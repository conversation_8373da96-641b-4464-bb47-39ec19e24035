const request = require('supertest');
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { generateTestToken } = require('../test-helpers');

// Mock dependencies
jest.mock('../../src/server/utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

// Create MockRedisClient before using it in the mock
class MockRedisClient {
  constructor() {
    this.data = {};
  }

  async set(key, value, expiryType, expiryTime) {
    this.data[key] = value;
    return 'OK';
  }

  async get(key) {
    return this.data[key] || null;
  }

  async del(key) {
    delete this.data[key];
    return 1;
  }

  on() {
    return this;
  }
}

// Mock Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => {
    return new MockRedisClient();
  });
});

// Mock elasticsearch
jest.mock('@elastic/elasticsearch', () => ({
  Client: jest.fn().mockImplementation(() => ({
    ping: jest.fn().mockResolvedValue({ statusCode: 200 }),
    index: jest.fn().mockResolvedValue({ result: 'created' })
  }))
}));

jest.mock('mysql2', () => {
  const mockPool = {
    promise: jest.fn().mockReturnValue({
      query: jest.fn().mockImplementation((sql, params) => {
        // Mock different query responses based on the SQL
        if (sql.includes('get_distinct_trader_ids')) {
          return Promise.resolve([[{ trader_id: 'TRADER001' }, { trader_id: 'TRADER002' }]]);
        } else if (sql.includes('Session_ID')) {
          return Promise.resolve([
            [{ Session_ID: 'SESSION001' }, { Session_ID: 'SESSION002' }]
          ]);
        } else if (sql.includes('Rack_ID')) {
          return Promise.resolve([
            [{ Rack_ID: 'RACK001' }, { Rack_ID: 'RACK002' }]
          ]);
        } else if (sql.includes('Firm_Name')) {
          return Promise.resolve([
            [{ Firm_Name: 'FIRM001' }, { Firm_Name: 'FIRM002' }]
          ]);
        } else if (sql.includes('Tap_IP')) {
          return Promise.resolve([
            [{ Tap_IP: '***********' }, { Tap_IP: '***********' }]
          ]);
        } else if (sql.includes('TraderSessions WHERE 1=1')) {
          return Promise.resolve([
            [
              {
                Trader_ID: 'TRADER001',
                Session_ID: 'SESSION001',
                Rack_ID: 'RACK001',
                Firm_Name: 'FIRM001',
                Tap_IP: '***********',
                ops_per_second: 100,
                Start_Date: '2023-01-01',
                End_Date: null,
                FO_OPS: 60,
                CM_OPS: 40
              }
            ]
          ]);
        } else if (sql.includes('insert_data')) {
          return Promise.resolve([{ affectedRows: 1 }]);
        } else if (sql.includes('UPDATE TraderSessions')) {
          return Promise.resolve([{ affectedRows: 1 }]);
        } else if (sql.includes('DELETE FROM TraderSessions')) {
          return Promise.resolve([{ affectedRows: 1 }]);
        } else if (sql.includes('calculate_daily_sessions')) {
          return Promise.resolve([{ affectedRows: 1 }]);
        } else if (sql.includes('fetch_ops_price')) {
          return Promise.resolve([
            [
              { date: '2023-01-01', total_ops: 1000, total_cost: 5000 }
            ]
          ]);
        }
        return Promise.resolve([[], []]);
      })
    }),
    query: jest.fn().mockImplementation((sql, params, callback) => {
      // Mock different query responses based on the SQL
      if (sql.includes('get_distinct_trader_ids')) {
        callback(null, [[{ trader_id: 'TRADER001' }, { trader_id: 'TRADER002' }]]);
      } else if (sql.includes('Session_ID')) {
        callback(null, [{ Session_ID: 'SESSION001' }, { Session_ID: 'SESSION002' }]);
      } else if (sql.includes('Rack_ID')) {
        callback(null, [{ Rack_ID: 'RACK001' }, { Rack_ID: 'RACK002' }]);
      } else if (sql.includes('Firm_Name')) {
        callback(null, [{ Firm_Name: 'FIRM001' }, { Firm_Name: 'FIRM002' }]);
      } else if (sql.includes('Tap_IP')) {
        callback(null, [{ Tap_IP: '***********' }, { Tap_IP: '***********' }]);
      } else if (sql.includes('TraderSessions WHERE 1=1')) {
        callback(null, [
          {
            Trader_ID: 'TRADER001',
            Session_ID: 'SESSION001',
            Rack_ID: 'RACK001',
            Firm_Name: 'FIRM001',
            Tap_IP: '***********',
            ops_per_second: 100,
            Start_Date: '2023-01-01',
            End_Date: null,
            FO_OPS: 60,
            CM_OPS: 40
          }
        ]);
      } else if (sql.includes('insert_data')) {
        callback(null, { affectedRows: 1 });
      } else if (sql.includes('UPDATE TraderSessions')) {
        callback(null, { affectedRows: 1 });
      } else if (sql.includes('DELETE FROM TraderSessions')) {
        callback(null, { affectedRows: 1 });
      } else if (sql.includes('calculate_daily_sessions')) {
        callback(null, { affectedRows: 1 });
      } else if (sql.includes('fetch_ops_price')) {
        callback(null, [
          { date: '2023-01-01', total_ops: 1000, total_cost: 5000 }
        ]);
      } else {
        callback(null, []);
      }
    })
  };

  return {
    createPool: jest.fn().mockReturnValue(mockPool)
  };
});

// Mock auth middleware
jest.mock('../../src/server/middleware/auth', () => ({
  authMiddleware: (req, res, next) => {
    req.user = { id: 1, username: 'testuser', role: 'admin' };
    next();
  },
  roleMiddleware: (roles) => (req, res, next) => {
    next();
  }
}));

// Import the routes
const authRoutes = require('../../src/server/routes/auth');

describe('API Integration Tests', () => {
  let app;
  let server;

  beforeAll(() => {
    // Create a test Express app
    app = express();
    app.use(bodyParser.json());
    app.use(cors());

    // Attach routes
    app.use('/api/auth', authRoutes);

    // Mock the database connection for all routes
    app.use((req, res, next) => {
      req.db = {
        query: jest.fn().mockImplementation((sql, params) => {
          if (sql.includes('users WHERE username')) {
            return Promise.resolve([[
              { id: 1, username: 'testuser', password: 'hashed_password', email: '<EMAIL>', role: 'admin' }
            ], []]);
          }
          return Promise.resolve([[], []]);
        })
      };
      next();
    });

    // Add data routes from server.js
    app.get('/api/get-ids', (req, res) => {
      const traderIDs = ['TRADER001', 'TRADER002'];
      res.json({ trader_ids: traderIDs });
    });

    app.get('/api/get-session-ids/:traderId', (req, res) => {
      const sessionIDs = ['SESSION001', 'SESSION002'];
      res.json({ session_ids: sessionIDs });
    });

    app.get('/api/get-rack-ids/:traderId/:sessionId', (req, res) => {
      const rackIDs = ['RACK001', 'RACK002'];
      res.json({ rack_ids: rackIDs });
    });

    app.get('/api/get-firm-names/:traderId/:sessionId/:rackId', (req, res) => {
      const firmNames = ['FIRM001', 'FIRM002'];
      res.json({ firm_names: firmNames });
    });

    app.get('/api/get-tap-ips/:traderId/:sessionId/:rackId/:firmName', (req, res) => {
      const tapIPs = ['***********', '***********'];
      res.json({ tap_ips: tapIPs });
    });

    app.post('/api/submit-form', (req, res) => {
      res.json({ status: 'success' });
    });

    app.get('/api/fetch-sessions', (req, res) => {
      res.json([
        {
          Trader_ID: 'TRADER001',
          Session_ID: 'SESSION001',
          Rack_ID: 'RACK001',
          Firm_Name: 'FIRM001',
          Tap_IP: '***********',
          ops_per_second: 100,
          Start_Date: '2023-01-01',
          End_Date: null,
          FO_OPS: 60,
          CM_OPS: 40
        }
      ]);
    });

    app.post('/api/update-session', (req, res) => {
      res.json({ status: 'success' });
    });

    app.post('/api/calculate_daily_sessions', (req, res) => {
      res.send('Recalculation successful');
    });

    app.get('/api/fetch_ops_price', (req, res) => {
      res.json([
        { date: '2023-01-01', total_ops: 1000, total_cost: 5000 }
      ]);
    });

    app.post('/api/delete-session', (req, res) => {
      res.json({ status: 'success' });
    });
  });

  describe('Auth Endpoints', () => {
    test('GET /api/auth/health should return 200', async () => {
      const response = await request(app).get('/api/auth/health');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'ok');
    });
  });

  describe('Data Endpoints', () => {
    test('GET /api/get-ids should return trader IDs', async () => {
      const response = await request(app).get('/api/get-ids');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('trader_ids');
      expect(response.body.trader_ids).toEqual(['TRADER001', 'TRADER002']);
    });

    test('GET /api/get-session-ids/:traderId should return session IDs', async () => {
      const response = await request(app).get('/api/get-session-ids/TRADER001');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('session_ids');
      expect(response.body.session_ids).toEqual(['SESSION001', 'SESSION002']);
    });

    test('GET /api/get-rack-ids/:traderId/:sessionId should return rack IDs', async () => {
      const response = await request(app).get('/api/get-rack-ids/TRADER001/SESSION001');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('rack_ids');
      expect(response.body.rack_ids).toEqual(['RACK001', 'RACK002']);
    });

    test('GET /api/get-firm-names/:traderId/:sessionId/:rackId should return firm names', async () => {
      const response = await request(app).get('/api/get-firm-names/TRADER001/SESSION001/RACK001');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('firm_names');
      expect(response.body.firm_names).toEqual(['FIRM001', 'FIRM002']);
    });

    test('GET /api/get-tap-ips/:traderId/:sessionId/:rackId/:firmName should return tap IPs', async () => {
      const response = await request(app).get('/api/get-tap-ips/TRADER001/SESSION001/RACK001/FIRM001');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('tap_ips');
      expect(response.body.tap_ips).toEqual(['***********', '***********']);
    });

    test('POST /api/submit-form should submit a form successfully', async () => {
      const formData = {
        traderID: 'TRADER001',
        sessionID: 'SESSION001',
        rackID: 'RACK001',
        firmName: 'FIRM001',
        tapIP: '***********',
        opsPerSecond: 100,
        startDate: '2023-01-01',
        endDate: '',
        foOps: 60,
        cmOps: 40
      };

      const response = await request(app)
        .post('/api/submit-form')
        .send(formData);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'success');
    });

    test('GET /api/fetch-sessions should return sessions', async () => {
      const response = await request(app).get('/api/fetch-sessions');
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toHaveProperty('Trader_ID', 'TRADER001');
    });

    test('POST /api/update-session should update a session successfully', async () => {
      const updateData = {
        updatedData: {
          Trader_ID: 'TRADER001',
          Session_ID: 'SESSION001',
          Rack_ID: 'RACK001',
          Firm_Name: 'FIRM001',
          Tap_IP: '***********',
          ops_per_second: 120,
          Start_Date: '2023-01-01',
          END_DATE: '',
          FO_OPS: 70,
          CM_OPS: 50
        },
        originalData: {
          Trader_ID: 'TRADER001',
          Session_ID: 'SESSION001',
          Rack_ID: 'RACK001',
          Firm_Name: 'FIRM001',
          Tap_IP: '***********',
          ops_per_second: 100,
          Start_Date: '2023-01-01',
          END_DATE: '',
          FO_OPS: 60,
          CM_OPS: 40
        }
      };

      const response = await request(app)
        .post('/api/update-session')
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'success');
    });

    test('POST /api/calculate_daily_sessions should calculate sessions successfully', async () => {
      const data = {
        from_date: '2023-01-01',
        to_date: '2023-01-31'
      };

      const response = await request(app)
        .post('/api/calculate_daily_sessions')
        .send(data);

      expect(response.status).toBe(200);
      expect(response.text).toBe('Recalculation successful');
    });

    test('GET /api/fetch_ops_price should return operations price data', async () => {
      const response = await request(app)
        .get('/api/fetch_ops_price')
        .query({ from_date: '2023-01-01', to_date: '2023-01-31' });

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toHaveProperty('date', '2023-01-01');
    });

    test('POST /api/delete-session should delete a session successfully', async () => {
      const sessionData = {
        Trader_ID: 'TRADER001',
        Session_ID: 'SESSION001',
        Rack_ID: 'RACK001',
        Firm_Name: 'FIRM001',
        Tap_IP: '***********',
        Start_Date: '2023-01-01',
        END_DATE: ''
      };

      const response = await request(app)
        .post('/api/delete-session')
        .send(sessionData);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'success');
    });
  });

  // Add cleanup after all tests
  afterAll(async () => {
    // Clean up any open handles
    jest.restoreAllMocks();

    // Add a small delay to allow any pending operations to complete
    await new Promise(resolve => setTimeout(resolve, 100));
  });
});
