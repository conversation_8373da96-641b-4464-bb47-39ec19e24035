const Redis = require('ioredis');
const config = require('../config/config');
const logger = require('./logger');

const redisClient = new Redis({
    host: config.REDIS_HOST,
    port: config.REDIS_PORT,
    password: config.REDIS_PASSWORD,
    retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
    },
});

redisClient.on('connect', () => {
    logger.info('Connected to Redis');
});

redisClient.on('error', (err) => {
    logger.error('Redis connection error:', err);
});

// Helper functions for token management
const tokenUtils = {
    // Add token to blacklist
    async blacklistToken(token, expiration) {
        try {
            await redisClient.set(`blacklist:${token}`, '1', 'EX', expiration);
            return true;
        } catch (error) {
            logger.error('Error blacklisting token:', error);
            return false;
        }
    },

    // Check if token is blacklisted
    async isTokenBlacklisted(token) {
        try {
            const result = await redisClient.get(`blacklist:${token}`);
            return result === '1';
        } catch (error) {
            logger.error('Error checking token blacklist:', error);
            return false;
        }
    },

    // Store refresh token
    async storeRefreshToken(userId, token, expiration) {
        try {
            await redisClient.set(`refresh:${userId}`, token, 'EX', expiration);
            return true;
        } catch (error) {
            logger.error('Error storing refresh token:', error);
            return false;
        }
    },

    // Get refresh token
    async getRefreshToken(userId) {
        try {
            return await redisClient.get(`refresh:${userId}`);
        } catch (error) {
            logger.error('Error getting refresh token:', error);
            return null;
        }
    },

    // Delete refresh token
    async deleteRefreshToken(userId) {
        try {
            await redisClient.del(`refresh:${userId}`);
            return true;
        } catch (error) {
            logger.error('Error deleting refresh token:', error);
            return false;
        }
    },
};

module.exports = { redisClient, tokenUtils };