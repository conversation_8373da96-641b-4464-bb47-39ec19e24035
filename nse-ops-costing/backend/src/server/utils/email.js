const nodemailer = require('nodemailer');
const config = require('../config/config');
const logger = require('./logger');

class EmailService {
    constructor() {
        this.transporter = nodemailer.createTransport({
            host: config.SMTP_HOST,
            port: config.SMTP_PORT,
            secure: config.SMTP_PORT === 465,
            auth: {
                user: config.SMTP_USER,
                pass: config.SMTP_PASS,
            },
        });
    }

    async sendPasswordResetEmail(user, resetToken) {
        try {
            const resetUrl = `${config.FRONTEND_URL}/reset-password?token=${resetToken}`;
            
            const mailOptions = {
                from: config.EMAIL_FROM,
                to: user.email,
                subject: 'Password Reset Request',
                html: `
                    <h1>Password Reset Request</h1>
                    <p>Hello ${user.username},</p>
                    <p>You have requested to reset your password. Click the link below to proceed:</p>
                    <p><a href="${resetUrl}">Reset Password</a></p>
                    <p>This link will expire in 1 hour.</p>
                    <p>If you did not request this password reset, please ignore this email.</p>
                `,
            };

            await this.transporter.sendMail(mailOptions);
            logger.info(`Password reset email sent to ${user.email}`);
            return true;
        } catch (error) {
            logger.error('Error sending password reset email:', error);
            throw error;
        }
    }

    async sendPasswordChangedEmail(user) {
        try {
            const mailOptions = {
                from: config.EMAIL_FROM,
                to: user.email,
                subject: 'Password Changed Successfully',
                html: `
                    <h1>Password Changed</h1>
                    <p>Hello ${user.username},</p>
                    <p>Your password has been successfully changed.</p>
                    <p>If you did not make this change, please contact support immediately.</p>
                `,
            };

            await this.transporter.sendMail(mailOptions);
            logger.info(`Password changed notification sent to ${user.email}`);
            return true;
        } catch (error) {
            logger.error('Error sending password changed email:', error);
            throw error;
        }
    }
}

module.exports = new EmailService(); 