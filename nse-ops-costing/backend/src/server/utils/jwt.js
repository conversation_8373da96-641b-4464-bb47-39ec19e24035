const jwt = require('jsonwebtoken');
const config = require('../config/config');
const { tokenUtils } = require('./redis');
const logger = require('./logger');

class JWTService {
    static async generateTokens(user) {
        try {
            // Generate access token
            const accessToken = jwt.sign(
                { 
                    userId: user.id,
                    username: user.username,
                    role: user.role 
                },
                config.JWT_SECRET,
                { expiresIn: config.JWT_EXPIRATION }
            );

            // Generate refresh token
            const refreshToken = jwt.sign(
                { userId: user.id },
                config.JWT_SECRET,
                { expiresIn: config.JWT_REFRESH_EXPIRATION }
            );

            // Store refresh token in Redis
            await tokenUtils.storeRefreshToken(
                user.id,
                refreshToken,
                parseInt(config.JWT_REFRESH_EXPIRATION) * 24 * 60 * 60 // Convert days to seconds
            );

            return {
                accessToken,
                refreshToken,
                expiresIn: config.JWT_EXPIRATION
            };
        } catch (error) {
            logger.error('Error generating tokens:', error);
            throw error;
        }
    }

    static async verifyToken(token) {
        try {
            // Check if token is blacklisted
            const isBlacklisted = await tokenUtils.isTokenBlacklisted(token);
            if (isBlacklisted) {
                throw new Error('Token has been blacklisted');
            }

            // Verify token
            const decoded = jwt.verify(token, config.JWT_SECRET);
            return decoded;
        } catch (error) {
            logger.error('Error verifying token:', error);
            throw error;
        }
    }

    static async refreshToken(refreshToken) {
        try {
            // Verify refresh token
            const decoded = jwt.verify(refreshToken, config.JWT_SECRET);
            
            // Get stored refresh token
            const storedToken = await tokenUtils.getRefreshToken(decoded.userId);
            if (!storedToken || storedToken !== refreshToken) {
                throw new Error('Invalid refresh token');
            }

            // Generate new tokens
            return await this.generateTokens(decoded);
        } catch (error) {
            logger.error('Error refreshing token:', error);
            throw error;
        }
    }

    static async invalidateToken(token) {
        try {
            const decoded = jwt.decode(token);
            if (!decoded || !decoded.exp) {
                throw new Error('Invalid token');
            }

            // Calculate remaining time until expiration
            const remainingTime = decoded.exp - Math.floor(Date.now() / 1000);
            if (remainingTime > 0) {
                await tokenUtils.blacklistToken(token, remainingTime);
            }

            // Delete refresh token if it exists
            if (decoded.userId) {
                await tokenUtils.deleteRefreshToken(decoded.userId);
            }

            return true;
        } catch (error) {
            logger.error('Error invalidating token:', error);
            throw error;
        }
    }
}

module.exports = JWTService; 