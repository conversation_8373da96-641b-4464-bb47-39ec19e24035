const { Client } = require('@elastic/elasticsearch');
const config = require('../config/config');

class ELKLogger {
    constructor() {
        this.client = new Client({
            node: `http://${config.ELASTICSEARCH_HOST}:${config.ELASTICSEARCH_PORT}`,
            //auth: {
            //    username: config.ELASTICSEARCH_USERNAME,
            //    password: config.ELASTICSEARCH_PASSWORD
            //},
            maxRetries: 5,
            requestTimeout: 30000,
            sniffOnStart: true,
            sniffInterval: 60000,
            sniffOnConnectionFault: true,
            tls: {
                rejectUnauthorized: false
            }
        });

        // Check connection on initialization
        //this.checkConnection();
    }

    async checkConnection() {
        try {
            const response = await this.client.ping();
            console.log('Elasticsearch connection successful:', response);
        } catch (error) {
            console.error('Failed to connect to Elasticsearch:', error);
            if (error.meta && error.meta.body) {
                console.error('Connection error details:', error.meta.body);
            }
        }
    }

    async log(level, message, meta = {}) {
        try {
            const timestamp = new Date().toISOString();
            const logEntry = {
                '@timestamp': timestamp, // <-- change this line
                level,
                message,
                ...meta
            };

            await this.client.index({
                index: `logs-${new Date().toISOString().split('T')[0]}`,
                body: logEntry,
                refresh: true
            });
        } catch (error) {
            console.error('Error logging to ELK:', error);
            if (error.meta && error.meta.body) {
                console.error('Elasticsearch error details:', error.meta.body);
            }
            //console.log("Error logging to ELK:");
        }
    }

    async error(message, meta = {}) {
        await this.log('error', message, meta);
    }

    async warn(message, meta = {}) {
        await this.log('warn', message, meta);
    }

    async info(message, meta = {}) {
        await this.log('info', message, meta);
    }

    async debug(message, meta = {}) {
        await this.log('debug', message, meta);
    }
}

module.exports = new ELKLogger();