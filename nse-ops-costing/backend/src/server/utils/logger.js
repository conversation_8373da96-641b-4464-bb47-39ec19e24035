const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const elkLogger = require('./elkLogger');

// Create Winston logger
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
    ),
    transports: [
        // Console transport
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                winston.format.simple()
            )
        }),
        // File transport with daily rotation
        new DailyRotateFile({
            filename: 'logs/application-%DATE%.log',
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '14d'
        })
    ]
});

// Create a wrapper function that logs to both Winston and ELK
const logToBoth = async (level, message, meta = {}) => {
    // Log to Winston
    logger.log(level, message, meta);

    // Log to ELK
    //try {
    //    await elkLogger.log(level, message, {
    //        ...meta,
    //        source: 'server',
    //        environment: process.env.NODE_ENV || 'development'
    //    });
    //} catch (error) {
        // If ELK logging fails, log the error to Winston
    //    logger.error('Failed to log to ELK:', error);
    //}
};

// Create the logging methods
const log = {
    error: (message, meta = {}) => logToBoth('error', message, meta),
    warn: (message, meta = {}) => logToBoth('warn', message, meta),
    info: (message, meta = {}) => logToBoth('info', message, meta),
    debug: (message, meta = {}) => logToBoth('debug', message, meta),
    http: (message, meta = {}) => logToBoth('http', message, meta)
};

module.exports = log; 