const express = require('express');
const mysql = require('mysql2');
const bodyParser = require('body-parser');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const useragent = require('useragent');

// Import our middleware and routes
const requestLogger = require('./src/server/middleware/requestLogger');
const errorLogger = require('./src/server/middleware/errorLogger');
const { authMiddleware, roleMiddleware } = require('./src/server/middleware/auth');
const authRoutes = require('./src/server/routes/auth');
const logger = require('./src/server/utils/logger');
const config = require('./src/server/config/config');

const app = express();
app.use(bodyParser.json());
app.use(cors()); // Enable CORS

// Initialize log file with headers if it doesn't exist
const logFilePath = path.join(__dirname, 'log.csv');
if (!fs.existsSync(logFilePath)) {
    const headers = 'timestamp,username,ip_address,method,path,status_code,request_body,response_body\n';
    fs.writeFileSync(logFilePath, headers);
}

// Apply logging middleware
app.use(requestLogger);

// Create MySQL connection pool
const pool = mysql.createPool({
    host: config.DB_HOST,
    user: config.DB_USER,
    password: config.DB_PASSWORD,
    database: config.DB_NAME,
    port: config.DB_PORT,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    enableKeepAlive: true,
    keepAliveInitialDelay: 0
});

// Middleware to attach database connection to request
app.use((req, res, next) => {
    // Use pool.promise() to get a promise-wrapper
    req.db = pool.promise();
    next();
});

// Authentication routes (no auth middleware needed)
app.use('/api/auth', authRoutes);

// Protected routes
app.use('/api', authMiddleware);

// Helper function to extract username from token
const getUsernameFromToken = (req) => {
    // First try to get username from X-Username header
    const usernameFromHeader = req.headers['x-username'];
    if (usernameFromHeader) {
        return usernameFromHeader;
    }

    // Fallback to token decoding if header not present
    const token = req.headers.authorization;
    if (!token) return 'anonymous';
    try {
        const base64Credentials = token.split(' ')[1];
        const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
        return credentials.split(':')[0];
    } catch (error) {
        logger.error('Error decoding token:', error);
        return 'anonymous';
    }
};

// Logging middleware
const auditLogger = (req, res, next) => {
    const originalEnd = res.end;
    const chunks = [];

    // Store response chunks
    res.write = function(chunk) {
        chunks.push(Buffer.from(chunk));
        return originalWrite.apply(this, arguments);
    };

    res.end = function(chunk, encoding) {
        if (chunk) {
            chunks.push(Buffer.from(chunk));
        }

        const timestamp = new Date().toISOString();
        const username = getUsernameFromToken(req);
        const ip = req.ip || req.connection.remoteAddress;
        const method = req.method;
        const path = req.path;
        const statusCode = res.statusCode;
        
        // Get request and response bodies
        let requestBody = JSON.stringify(req.body || {});
        let responseBody = Buffer.concat(chunks).toString('utf8');
        
        // Truncate response and request bodies to 1000 characters for GET requests
        if (method === 'GET') {
            requestBody = requestBody.substring(0, 1000);
            responseBody = responseBody.substring(0, 1000);
        }
        
        // Log the request
        const logEntry = `${timestamp},${username},${ip},${method},${path},${statusCode},"${requestBody}","${responseBody}"\n`;
        
        fs.appendFile(logFilePath, logEntry, (err) => {
            if (err) {
                console.error('Error writing to log file:', err);
            }
        });
        
        originalEnd.call(this, chunk, encoding);
    };
    
    next();
};

// Apply logging middleware to all routes
app.use(auditLogger);

// API to fetch all Trader IDs
app.get('/api/get-trader-ids', (req, res) => {
    pool.query('CALL get_distinct_trader_ids()', (err, traderResults) => {
        if (err) {
            logger.error('Error fetching Trader IDs:', err);
            return res.status(500).json({ error: 'Failed to fetch Trader IDs' });
        }
        const traderIDs = traderResults[0].map((row) => row.trader_id);
        res.json({ trader_ids: traderIDs });
    });
});

// API to fetch Client IDs for a specific Trader ID
app.get('/api/get-client-ids/:traderID', (req, res) => {
    const { traderID } = req.params;
    pool.query('SELECT DISTINCT Client_ID FROM TraderSessions WHERE Trader_ID = ?', [traderID], (err, results) => {
        if (err) {
            logger.error('Error fetching Client IDs:', err);
            return res.status(500).json({ error: 'Failed to fetch Client IDs' });
        }
        const clientIDs = results.map(row => row.Client_ID);
        res.json({ client_ids: clientIDs });
    });
});

// API to fetch Rack IDs for a specific Client ID and Trader ID
app.get('/api/get-rack-ids/:clientId/:traderId', (req, res) => {
    const { clientId, traderId } = req.params;
    const query = 'SELECT DISTINCT Rack_ID FROM TraderSessions WHERE Client_ID = ? AND Trader_ID = ?';
    pool.query(query, [clientId, traderId], (err, results) => {
        if (err) {
            logger.error('Error fetching Rack IDs:', err);
            return res.status(500).json({ error: 'Failed to fetch Rack IDs' });
        }
        const rackIDs = results.map(row => row.Rack_ID);
        res.json({ rack_ids: rackIDs });
    });
});

// API to fetch Firm Names for a specific Client ID, Trader ID, and Rack ID
app.get('/api/get-firm-names/:clientId/:traderId/:rackId', (req, res) => {
    const { clientId, traderId, rackId } = req.params;
    const query = 'SELECT DISTINCT Firm_Name FROM TraderSessions WHERE Client_ID = ? AND Trader_ID = ? AND Rack_ID = ?';
    pool.query(query, [clientId, traderId, rackId], (err, results) => {
        if (err) {
            logger.error('Error fetching Firm Names:', err);
            return res.status(500).json({ error: 'Failed to fetch Firm Names' });
        }
        const firmNames = results.map(row => row.Firm_Name);
        res.json({ firm_names: firmNames });
    });
});

// API to fetch Tap IPs for a specific combination
app.get('/api/get-tap-ips/:clientId/:traderId/:rackId/:firmName', (req, res) => {
    const { clientId, traderId, rackId, firmName } = req.params;
    const query = 'SELECT DISTINCT Tap_IP FROM TraderSessions WHERE Client_ID = ? AND Trader_ID = ? AND Rack_ID = ? AND Firm_Name = ?';
    pool.query(query, [clientId, traderId, rackId, firmName], (err, results) => {
        if (err) {
            logger.error('Error fetching Tap IPs:', err);
            return res.status(500).json({ error: 'Failed to fetch Tap IPs' });
        }
        const tapIPs = results.map(row => row.Tap_IP);
        res.json({ tap_ips: tapIPs });
    });
});

// API to handle form submission
app.post('/api/submit-form', (req, res) => {
    const formData = req.body;
    endDate = formData.endDate === '' ? null : formData.endDate;
    // Ensure startDate is in YYYY-MM-DD format (remove time part if present)
    let startDate = formData.startDate;
    if (typeof startDate === 'string' && startDate.length >= 10) {
        startDate = startDate.substring(0, 10);
    }
    if (typeof endDate === 'string' && endDate.length >= 10) {
        endDate = endDate.substring(0, 10);
    }
    // Ensure startDate and endDate are in YYYY-MM-DD format
    const formatDate = (date) => {
        if (!date) return null;
        const d = new Date(date);
        if (isNaN(d.getTime())) return null;
        return d.toISOString().slice(0, 10);
    };
    const formattedStartDate = formatDate(startDate);
    const formattedEndDate = formatDate(endDate);
    //logger.info(`Formatted Start Date: ${formattedStartDate}`);
    //logger.info(`Formatted End Date: ${formattedEndDate}`);
    pool.query(
        'CALL insert_data(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
            formData.traderID,
            formData.clientID,
            formData.rackID,
            formData.firmName,
            formData.tapIP,
            formData.opsPerSecond,
            formattedStartDate,
            formattedEndDate,
            formData.foOps,
            formData.cmOps,
            formData.linkType,
        ],
        (err, results) => {
            if (err) {
                logger.error('Error inserting data:', err);
                return res.status(500).json({ error: 'Failed to insert data' });
            }
            res.json({ status: 'success' });
        }
    );
});

// API to fetch sessions with filters
app.get('/api/fetch-sessions', (req, res) => {
    const {
        traderID,
        clientID,
        rackID,
        firmName,
        tapIP,
        startDate,
        endDate,
        activeOnly
    } = req.query;

    let query = `
        SELECT *,
            DATE_FORMAT(Start_Date, '%Y-%m-%d') AS Start_Date,
            DATE_FORMAT(END_DATE, '%Y-%m-%d') AS END_DATE,
            LinkType
        FROM TraderSessions 
        WHERE 1=1
    `;
    const queryParams = [];

    if (traderID) {
        // Handle pipe-separated trader IDs
        const traderIDs = traderID.split('|');
        query += ` AND (Trader_ID LIKE ?`;
        queryParams.push(`%${traderIDs[0]}%`);
        for (let i = 1; i < traderIDs.length; i++) {
            query += ` OR Trader_ID LIKE ?`;
            queryParams.push(`%${traderIDs[i]}%`);
        }
        query += `)`;
    }
    if (clientID) {
        // Handle pipe-separated client IDs
        const clientIDs = clientID.split('|');
        query += ` AND (Client_ID LIKE ?`;
        queryParams.push(`%${clientIDs[0]}%`);
        for (let i = 1; i < clientIDs.length; i++) {
            query += ` OR Client_ID LIKE ?`;
            queryParams.push(`%${clientIDs[i]}%`);
        }
        query += `)`;
    }
    if (rackID) {
        query += ' AND Rack_ID = ?';
        queryParams.push(rackID);
    }
    if (firmName) {
        query += ' AND Firm_Name = ?';
        queryParams.push(firmName);
    }
    if (tapIP) {
        query += ' AND Tap_IP = ?';
        queryParams.push(tapIP);
    }
    if (startDate) {
        query += ' AND Start_Date >= ?';
        queryParams.push(startDate);
    }
    if (endDate) {
        query += ' AND End_Date <= ?';
        queryParams.push(endDate);
    }
    // Add active sessions filter if requested
    if (activeOnly === 'true') {
        query += ' AND End_Date IS NULL';
    }
    // Add ORDER BY clause for better organization
    query += ' ORDER BY Start_Date DESC';

    pool.query(query, queryParams, (err, results) => {
        if (err) {
            logger.error('Error fetching sessions:', err);
            return res.status(500).json({ error: 'Failed to fetch sessions' });
        }
        res.json(results);
    });
});

// API to update session details
app.post('/api/update-session', (req, res) => {
    const { updatedData, originalData } = req.body;
    // Validate required fields
    if (!updatedData || !originalData) {
        return res.status(400).json({ error: 'Both updated and original data are required' });
    }
    // Validate FO_OPS + CM_OPS = ops_per_second
    if (updatedData.FO_OPS + updatedData.CM_OPS !== updatedData.ops_per_second) {
        return res.status(400).json({ error: 'FO_OPS + CM_OPS must equal ops_per_second' });
    }
    
    // Convert empty END_DATE to NULL
    const updatedEndDate = updatedData.END_DATE === '' ? null : updatedData.END_DATE;
    const originalEndDate = originalData.END_DATE === '' ? null : originalData.END_DATE;
    const query = `
        UPDATE TraderSessions 
        SET             
            Trader_ID = ?,
            Client_ID = ?,
            Rack_ID = ?,
            Firm_Name = ?,
            Tap_IP = ?,
            ops_per_second = ?,
            Start_Date = ?,
            FO_OPS = ?,
            CM_OPS = ?,
            END_DATE = ?,
            LinkType = ?
        WHERE 
            Trader_ID = ? AND 
            Client_ID = ? AND
            Rack_ID = ? AND 
            Firm_Name = ? AND 
            Tap_IP = ? AND 
            Start_Date = ? AND
            (END_DATE = ?)
    `;
    pool.query(
        query,
        [
            updatedData.Trader_ID,
            updatedData.Client_ID,
            updatedData.Rack_ID,
            updatedData.Firm_Name,
            updatedData.Tap_IP,
            updatedData.ops_per_second,
            updatedData.Start_Date,
            updatedData.FO_OPS,
            updatedData.CM_OPS,
            updatedEndDate,
            updatedData.LinkType,
            originalData.Trader_ID,
            originalData.Client_ID,
            originalData.Rack_ID,
            originalData.Firm_Name,
            originalData.Tap_IP,
            originalData.Start_Date,
            originalEndDate
            
        ],
        (err, results) => {
            
                logger.error('Error updating session:', err);
                // Always print the executed SQL and parameters
                 console.log('Executed SQL:', query, [
                    updatedData.Trader_ID,
                    updatedData.Client_ID,
                    updatedData.Rack_ID,
                    updatedData.Firm_Name,
                    updatedData.Tap_IP,
                    updatedData.ops_per_second,
                    updatedData.Start_Date,
                    updatedData.FO_OPS,
                    updatedData.CM_OPS,
                    updatedEndDate,
                    updatedData.LinkType,
                    originalData.Trader_ID,
                    originalData.Client_ID,
                    originalData.Rack_ID,
                    originalData.Firm_Name,
                    originalData.Tap_IP,
                    originalData.Start_Date,
                    originalEndDate,
                    
                ]); 
                if (err) {
                    logger.error('Error updating session:', err);
                    return res.status(500).json({ error: 'Failed to update session' });
                }
                if (results.affectedRows === 0) {
                    return res.status(404).json({ error: 'Session not found or no changes made' });
                }
                if (results.affectedRows > 1) {
                    logger.warn('Warning: Multiple rows were updated');
                }
            res.json({ status: 'success' });
        
        });    
});

// API to delete a session
app.post('/api/delete-session', (req, res) => {
    const sessionData = req.body;
    // Convert empty END_DATE to NULL
    const endDate = sessionData.END_DATE === '' ? null : sessionData.END_DATE;
    const query = `
        DELETE FROM TraderSessions 
        WHERE 
            Client_ID = ? AND
            Trader_ID = ? AND 
            Rack_ID = ? AND 
            Firm_Name = ? AND 
            Tap_IP = ? AND 
            Start_Date = ? AND
            (END_DATE = ? OR (END_DATE IS NULL AND ? IS NULL)) AND
            LinkType = ?
    `;
    pool.query(
        query,
        [
            sessionData.Client_ID,
            sessionData.Trader_ID,
            sessionData.Rack_ID,
            sessionData.Firm_Name,
            sessionData.Tap_IP,
            sessionData.Start_Date,
            endDate,
            endDate,
            sessionData.LinkType
        ],
        (err, results) => {
            if (err) {
                logger.error('Error deleting session:', err);
                return res.status(500).json({ error: 'Failed to delete session' });
            }
            if (results.affectedRows === 0) {
                logger.error('Session not found');
                return res.status(500).json({ error: 'Session not found' });
            }
            res.json({ status: 'success' });
        }
    );
});

app.post('/api/calculate_daily_sessions', (req, res) => {
    const { from_date, to_date } = req.body;

    const query = `
        CALL calculate_daily_sessions(?, ?);
    `;

    db.query(query, [from_date, to_date], (error) => {
        if (error) {
            console.error('Error executing query:', error);
            res.status(500).send('Error executing query');
        } else {
            res.send('Recalculation successful');
        }
    });
});

app.post('/api/fetch_ops_price', (req, res) => {
    const { from_date, to_date } = req.body;
    	
    const query = `
        CALL fetch_ops_price(?, ?);
    `;

    db.query(query, [from_date, to_date], (error, results) => {
        if (error) {
            console.error('Error executing query:', error);
            res.status(500).send('Error executing query');
        } else {
            res.send(results[0]);
        }
    });
});

// Apply error logging middleware
app.use(errorLogger);

const port = config.PORT;
app.listen(port, () => {
    logger.info(`Server running on port ${port}`);
});

