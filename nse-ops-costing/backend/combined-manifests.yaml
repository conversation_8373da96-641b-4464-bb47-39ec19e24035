apiVersion: v1
kind: ConfigMap
metadata:
  name: nse-ops-backend-config
  namespace: default
data:
  PORT: "3001"
  NODE_ENV: "production"
  REDIS_HOST: "redis-master.redis.svc.cluster.local"
  REDIS_PORT: "6379"
  FRONTEND_URL: "http://nse-ops-frontend-service"
  ELASTICSEARCH_HOST: "quickstart-es-http.elastic-system.svc.cluster.local"
  ELASTICSEARCH_PORT: "9200"
---
apiVersion: v1
kind: Secret
metadata:
  name: nse-ops-backend-secret
  namespace: default
type: Opaque
data:
  JWT_SECRET: ${JWT_SECRET_BASE64}
  REDIS_PASSWORD: ${REDIS_PASSWORD_BASE64}
  DB_HOST: ${DB_HOST_BASE64}
  DB_PORT: ${DB_PORT_BASE64}
  DB_USER: ${DB_USER_BASE64}
  DB_PASSWORD: ${DB_PASSWORD_BASE64}
  DB_NAME: ${DB_NAME_BASE64}
  SMTP_HOST: ${SMTP_HOST_BASE64}
  SMTP_PORT: ${SMTP_PORT_BASE64}
  SMTP_USER: ${SMTP_USER_BASE64}
  SMTP_PASS: ${SMTP_PASS_BASE64}
  EMAIL_FROM: ${EMAIL_FROM_BASE64}
  ELASTICSEARCH_USERNAME: ${ELASTICSEARCH_USERNAME_BASE64}
  ELASTICSEARCH_PASSWORD: ${ELASTICSEARCH_PASSWORD_BASE64}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nse-ops-backend
  namespace: default
  labels:
    app: nse-ops-backend
spec:
  replicas: 2  # Default replicas, will be managed by HPA
  selector:
    matchLabels:
      app: nse-ops-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:
      labels:
        app: nse-ops-backend
    spec:
      containers:
        - name: nse-ops-backend
          image: ${FULL_IMAGE_REFERENCE}
          ports:
            - containerPort: 3001
          imagePullPolicy: Always
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
          envFrom:
            - configMapRef:
                name: nse-ops-backend-config
            - secretRef:
                name: nse-ops-backend-secret
          readinessProbe:
            httpGet:
              path: /api/auth/health
              port: 3001
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /api/auth/health
              port: 3001
            initialDelaySeconds: 30
            periodSeconds: 30
      imagePullSecrets:
        - name: aws-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: nse-ops-backend-service
  namespace: default
  labels:
    app: nse-ops-backend
spec:
  type: ClusterIP
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
    name: http
  selector:
    app: nse-ops-backend
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nse-ops-backend-hpa
  namespace: default
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nse-ops-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
