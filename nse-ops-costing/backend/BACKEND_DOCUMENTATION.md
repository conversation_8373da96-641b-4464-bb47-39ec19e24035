# NSE Operations Costing Backend Documentation

This document provides detailed information about the NSE Operations Costing Backend application, including its architecture, components, and functionality.

## Table of Contents

- [Architecture](#architecture)
- [Core Components](#core-components)
- [Database Schema](#database-schema)
- [API Endpoints](#api-endpoints)
- [Authentication and Authorization](#authentication-and-authorization)
- [Logging and Monitoring](#logging-and-monitoring)
- [Error Handling](#error-handling)
- [Performance Considerations](#performance-considerations)

## Architecture

The NSE Operations Costing Backend is built using Node.js and Express.js. It follows a modular architecture with clear separation of concerns:

### High-Level Architecture

```
Client <-> Express Server <-> Database (MySQL)
                          <-> Cache (Redis)
                          <-> Logging (ELK Stack)
```

### Key Technologies

- **Node.js**: JavaScript runtime
- **Express.js**: Web framework
- **MySQL**: Relational database
- **Redis**: In-memory data store for caching and token management
- **JWT**: JSON Web Tokens for authentication
- **Winston**: Logging library
- **Elasticsearch**: Log storage and analysis

## Core Components

### 1. Server Configuration (`server.js`)

The main entry point of the application. It:
- Sets up the Express server
- Configures middleware
- Establishes database connections
- Defines API routes
- Handles error logging

### 2. Configuration (`src/server/config/config.js`)

Manages application configuration using environment variables with sensible defaults.

### 3. Middleware

- **Authentication Middleware** (`src/server/middleware/auth.js`): Validates JWT tokens and enforces role-based access control
- **Request Logger** (`src/server/middleware/requestLogger.js`): Logs incoming requests
- **Error Logger** (`src/server/middleware/errorLogger.js`): Logs application errors

### 4. Routes

- **Authentication Routes** (`src/server/routes/auth.js`): Handles user authentication, including login, logout, token refresh, and password reset

### 5. Utilities

- **JWT Service** (`src/server/utils/jwt.js`): Manages JWT token creation, validation, and invalidation
- **Email Service** (`src/server/utils/email.js`): Sends emails for password reset
- **Logger** (`src/server/utils/logger.js`): Configures Winston for application logging
- **Redis Client** (`src/server/utils/redis.js`): Manages Redis connections
- **ELK Logger** (`src/server/utils/elkLogger.js`): Sends logs to Elasticsearch

## Database Schema

The application uses a MySQL database with the following main tables:

### TraderSessions Table

Stores information about trader sessions:

| Column         | Type         | Description                       |
|----------------|--------------|-----------------------------------|
| Trader_ID      | VARCHAR(255) | ID of the trader                  |
| Session_ID     | VARCHAR(255) | Session identifier                |
| Rack_ID        | VARCHAR(255) | Rack identifier                   |
| Firm_Name      | VARCHAR(255) | Name of the firm                  |
| Tap_IP         | VARCHAR(255) | IP address of the tap             |
| ops_per_second | INT          | Operations per second             |
| Start_Date     | DATETIME     | Start date and time of the session|
| End_Date       | DATETIME     | End date and time of the session  |
| FO_OPS         | INT          | Front office operations           |
| CM_OPS         | INT          | Cash market operations            |

### Users Table

Stores user information:

| Column   | Type         | Description                |
|----------|--------------|----------------------------|
| id       | INT          | User ID (auto-increment)   |
| username | VARCHAR(255) | Username                   |
| password | VARCHAR(255) | Hashed password            |
| email    | VARCHAR(255) | Email address              |
| role     | VARCHAR(50)  | User role (admin, user)    |

## API Endpoints

### Authentication Endpoints

- `POST /api/auth/login`: Authenticate a user
  - Request: `{ username, password }`
  - Response: `{ token, refreshToken, user }`

- `POST /api/auth/logout`: Invalidate a token
  - Request: JWT token in Authorization header
  - Response: `{ success, message }`

- `POST /api/auth/refresh-token`: Get a new access token
  - Request: `{ refreshToken }`
  - Response: `{ token, refreshToken }`

- `POST /api/auth/request-password-reset`: Request password reset
  - Request: `{ email }`
  - Response: `{ message }`

- `POST /api/auth/reset-password`: Reset password
  - Request: `{ token, newPassword }`
  - Response: `{ message }`

- `GET /api/auth/health`: Health check endpoint
  - Response: `{ status, timestamp }`

### Data Endpoints

- `GET /api/get-ids`: Get all trader IDs
  - Response: `{ trader_ids }`

- `GET /api/get-session-ids/:traderId`: Get session IDs for a trader
  - Response: `{ session_ids }`

- `GET /api/get-rack-ids/:traderId/:sessionId`: Get rack IDs
  - Response: `{ rack_ids }`

- `GET /api/get-firm-names/:traderId/:sessionId/:rackId`: Get firm names
  - Response: `{ firm_names }`

- `GET /api/get-tap-ips/:traderId/:sessionId/:rackId/:firmName`: Get tap IPs
  - Response: `{ tap_ips }`

- `POST /api/submit-form`: Submit a new trader session
  - Request: `{ traderID, sessionID, rackID, firmName, tapIP, opsPerSecond, startDate, endDate, foOps, cmOps }`
  - Response: `{ status }`

- `GET /api/fetch-sessions`: Fetch trader sessions with filters
  - Query Parameters: `traderID, sessionID, rackID, firmName, tapIP, startDate, endDate, activeOnly`
  - Response: Array of trader sessions

- `POST /api/update-session`: Update an existing trader session
  - Request: `{ updatedData, originalData }`
  - Response: `{ status }`

- `POST /api/calculate_daily_sessions`: Calculate daily sessions
  - Request: `{ from_date, to_date }`
  - Response: Success message

- `GET /api/fetch_ops_price`: Fetch operations price
  - Query Parameters: `from_date, to_date`
  - Response: Operations price data

- `POST /api/delete-session`: Delete a trader session
  - Request: Session data
  - Response: `{ status }`

## Authentication and Authorization

The application uses JWT (JSON Web Tokens) for authentication:

1. **Access Tokens**: Short-lived tokens (15 minutes) used for API access
2. **Refresh Tokens**: Long-lived tokens (7 days) used to obtain new access tokens
3. **Token Storage**: Refresh tokens are stored in Redis for validation and revocation

### Authentication Flow

1. User logs in with username and password
2. Server validates credentials and generates access and refresh tokens
3. Client includes access token in Authorization header for API requests
4. When access token expires, client uses refresh token to get a new access token
5. On logout, tokens are invalidated

### Role-Based Access Control

The application supports role-based access control through the `roleMiddleware` function, which restricts access to certain endpoints based on user roles.

## Logging and Monitoring

The application uses a comprehensive logging system:

1. **Request Logging**: All API requests are logged with details like timestamp, user, IP, method, path, status code, request body, and response body
2. **Error Logging**: Application errors are logged with stack traces
3. **Winston Logger**: Configures log levels, formats, and transports
4. **Daily Rotate File**: Rotates log files daily to prevent large files
5. **Elasticsearch Integration**: Sends logs to Elasticsearch for centralized logging and analysis

## Error Handling

The application uses a centralized error handling approach:

1. **Try-Catch Blocks**: All route handlers use try-catch blocks to catch errors
2. **Error Middleware**: Catches unhandled errors and logs them
3. **Consistent Error Responses**: All error responses follow a consistent format
4. **Detailed Logging**: Errors are logged with context information for debugging

## Performance Considerations

The application includes several performance optimizations:

1. **Connection Pooling**: Uses MySQL connection pooling to efficiently manage database connections
2. **Redis Caching**: Uses Redis for token storage and caching
3. **Proper Indexing**: Ensures database tables are properly indexed for efficient queries
4. **Query Optimization**: Uses parameterized queries and stored procedures for efficient database access
5. **Resource Limits**: Sets appropriate resource limits in Kubernetes deployment

## Stored Procedures

The application uses several stored procedures for efficient data processing:

1. **get_distinct_trader_ids**: Retrieves distinct trader IDs
2. **insert_data**: Inserts a new trader session
3. **calculate_daily_sessions**: Calculates daily sessions for a date range
4. **fetch_ops_price**: Fetches operations price for a date range

## Security Considerations

The application implements several security measures:

1. **Password Hashing**: Uses bcrypt for secure password storage
2. **JWT Authentication**: Secures API endpoints
3. **HTTPS**: Assumes deployment behind HTTPS
4. **Input Validation**: Validates all user inputs
5. **Parameterized Queries**: Prevents SQL injection
6. **Rate Limiting**: Can be implemented at the infrastructure level
7. **Secure Headers**: Can be added using middleware
