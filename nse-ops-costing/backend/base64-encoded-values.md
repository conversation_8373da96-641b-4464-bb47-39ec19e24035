# Base64 Encoded Values for Kubernetes Secrets

These values are generated from the default production values in `src/server/config/config.js`. You should use these as a reference and replace them with your actual production values in your Bitbucket repository variables.

## How to Use These Values

Add these values as repository variables in your Bitbucket repository settings. The pipeline will use these variables when generating the Kubernetes manifests.

## Base64 Encoded Values

```yaml
JWT_SECRET_BASE64: eW91ci1zZWNyZXQta2V5
REDIS_PASSWORD_BASE64: RkNmeFJLbUZETg==
DB_HOST_BASE64: bXlzcWwtc2VydmljZQ==
DB_PORT_BASE64: MzMwNg==
DB_USER_BASE64: cm9vdA==
DB_PASSWORD_BASE64: c3Ryb25nX3Bhc3N3b3Jk
DB_NAME_BASE64: bnNlX2Nvc3Rpbmc=
SMTP_HOST_BASE64: c210cC5nbWFpbC5jb20=
SMTP_PORT_BASE64: NTg3
SMTP_USER_BASE64: ************************************
SMTP_PASS_BASE64: bmJ0eCBmYWh6IHZjc3YgdWxmeA==
EMAIL_FROM_BASE64: ************************************
ELASTICSEARCH_USERNAME_BASE64: ZWxhc3RpYw==
ELASTICSEARCH_PASSWORD_BASE64: dXlRQnA4djl4aEo0VE00NVU5OTMwcnY5
```

## Security Considerations

- These values are based on the default values in your config file and should be replaced with your actual production values.
- Store sensitive values securely in your Bitbucket repository variables.
- Consider using a secrets management solution for production environments.
- Regularly rotate passwords and secrets.

## How to Generate Your Own Base64 Encoded Values

If you need to generate your own base64 encoded values, you can use the following command:

```bash
echo -n 'your-value' | base64
```

To decode a base64 encoded value:

```bash
echo 'base64-encoded-value' | base64 --decode
```
