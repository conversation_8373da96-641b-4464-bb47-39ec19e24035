{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "build": "npm ci && npm run lint && echo 'Build completed successfully'", "lint": "eslint --ext .js ./src ./server.js || true", "test": "jest --coverage --bail --ci --forceExit", "test:unit": "jest --testPathPattern=tests/unit --bail --ci --forceExit", "test:integration": "cross-env NODE_ENV=test jest --testPathPattern=tests/integration --bail --ci --forceExit", "test:debug": "cross-env NODE_ENV=test jest --detectOpenHandles", "test:watch": "jest --watch"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"@ag-grid-community/client-side-row-model": "^32.3.3", "@elastic/elasticsearch": "^7.17.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "mysql": "^2.18.1", "mysql2": "^3.14.0", "nodemailer": "^6.10.0", "useragent": "^2.3.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@types/jest": "^29.5.14", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "eslint": "^9.24.0", "jest": "^29.7.0", "supertest": "^7.1.0"}}