# NSE OPS Costing Frontend Documentation

## Table of Contents

1. [User Guide](#user-guide)
   - [Application Overview](#application-overview)
   - [Features](#features)
   - [User Workflows](#user-workflows)
   - [Troubleshooting](#troubleshooting)

2. [Implementation Guide](#implementation-guide)
   - [Architecture Overview](#architecture-overview)
   - [Component Structure](#component-structure)
   - [Authentication Flow](#authentication-flow)
   - [API Integration](#api-integration)
   - [State Management](#state-management)
   - [Styling Approach](#styling-approach)
   - [Data Grid Implementation](#data-grid-implementation)

3. [Deployment Guide](#deployment-guide)
   - [Local Development](#local-development)
   - [Production Deployment](#production-deployment)
   - [Environment Variables](#environment-variables)
   - [Container Configuration](#container-configuration)
   - [Kubernetes Deployment](#kubernetes-deployment)

---

# User Guide

## Application Overview

The NSE OPS Costing application is a web-based tool designed to manage and track operational costs for NSE (National Stock Exchange) operations. It provides functionality for inputting trader session data, viewing billing information, and managing sessions.

## Features

### Authentication
- Secure login system
- Password reset functionality
- Role-based access control (user/admin)

### Data Input
- Trader session form for data entry
- Multi-select trader options
- Session, rack, firm, and tap IP selection
- Operations per second configuration
- Date range selection

### Data Visualization
- View bills with date range filtering
- AG Grid integration for advanced data display
- Grouping and filtering capabilities
- Export functionality

### Session Management
- Edit existing sessions
- View historical session data
- Update session parameters

## User Workflows

### Login Process
1. Navigate to the login page
2. Enter username and password
3. Click "Login" button
4. If credentials are valid, you will be redirected to the main application
5. If you forgot your password, click "Forgot Password" and follow the instructions

### Inputting Trader Session Data
1. Navigate to the "Input" page from the header navigation
2. Fill in the trader session form:
   - Select trader(s)
   - Choose session ID
   - Select rack ID
   - Enter firm name
   - Enter tap IP
   - Configure operations per second
   - Set FO and CM operations
   - Select start and end dates
3. Click "Submit" to save the data
4. A confirmation message will appear upon successful submission

### Viewing Bills
1. Navigate to the "View Bills" page from the header navigation
2. Select date range using the date pickers
3. Click "Fetch Results" to load the data
4. Use AG Grid features to:
   - Sort columns
   - Filter data
   - Group by different parameters
   - Export data to CSV

### Editing Sessions
1. Navigate to the "Edit Sessions" page from the header navigation
2. View existing sessions in the table
3. Select a session to edit
4. Modify session parameters as needed
5. Save changes

### Logging Out
1. Click the "Logout" button in the header
2. You will be redirected to the login page

## Troubleshooting

### Common Issues

1. **Login Problems**
   - Ensure caps lock is off
   - Verify username and password
   - Check network connectivity
   - If persistent, use the "Forgot Password" feature

2. **Form Submission Errors**
   - Ensure all required fields are filled
   - Verify that FO_OPS + CM_OPS equals ops_per_second
   - Check network connectivity

3. **Data Display Issues**
   - Try refreshing the page
   - Clear browser cache
   - Ensure date range is valid

4. **Session Timeout**
   - Log in again if your session has expired
   - The system will automatically redirect to the login page

---

# Implementation Guide

## Architecture Overview

The NSE OPS Costing frontend is built using React.js with a component-based architecture. It follows modern React patterns including hooks, context API for state management, and React Router for navigation.

### Technology Stack
- **React.js**: Core UI library
- **React Router**: Navigation and routing
- **AG Grid**: Advanced data grid for displaying tabular data
- **Axios**: HTTP client for API requests
- **React DatePicker**: Date selection components
- **JWT**: Authentication token management

## Component Structure

The application follows a hierarchical component structure:

```
App
├── AuthProvider (Context)
├── Routes
│   ├── Public Routes
│   │   ├── Login
│   │   ├── ForgotPassword
│   │   └── ResetPassword
│   └── Protected Routes
│       ├── Header
│       ├── InputPage
│       │   └── TraderSessionForm
│       ├── OpsPriceGrid
│       └── EditSessionsPage
└── Shared Components
    ├── LoadingSpinner
    └── ProtectedRoute
```

### Key Components

- **App.js**: Main application component that sets up routing and context providers
- **AuthContext.js**: Manages authentication state and provides auth-related functions
- **Header.js**: Navigation header with links to different sections
- **InputPage.js**: Page for inputting trader session data
- **TraderSessionForm.js**: Form component for session data entry
- **OpsPriceGrid.js**: Data grid for displaying billing information
- **EditSessionsPage.js**: Page for editing existing sessions

## Authentication Flow

The application uses JWT (JSON Web Tokens) for authentication:

1. **Login**: User credentials are sent to the backend, which returns access and refresh tokens
2. **Token Storage**: Tokens are stored in localStorage
3. **Token Usage**: Access token is included in the Authorization header for API requests
4. **Token Refresh**: Refresh token is used to obtain a new access token when the current one expires
5. **Logout**: Tokens are removed from localStorage

### Authentication Context

The `AuthContext` provides:
- Current user information
- Login function
- Logout function
- Password reset functions
- Authentication state (loading, error)

## API Integration

The application communicates with the backend API using the `fetchWithAuth` utility:

```javascript
export const fetchWithAuth = async (endpoint, options = {}) => {
    const token = localStorage.getItem('accessToken');
    const username = localStorage.getItem('username');
    
    if (!token) {
        throw new Error('No authentication token found');
    }

    const headers = {
        'Authorization': `Bearer ${token}`,
        'X-Username': username,
        'Content-Type': 'application/json',
        ...options.headers
    };

    try {
        const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}${endpoint}`, {
            ...options,
            headers
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response;
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
};
```

### Key API Endpoints

- `/api/auth/login`: User authentication
- `/api/auth/refresh-token`: Token refresh
- `/api/auth/forgot-password`: Password reset request
- `/api/auth/reset-password`: Password reset
- `/api/submit-form`: Submit trader session data
- `/api/fetch_ops_price`: Fetch billing data
- `/api/calculate_daily_sessions`: Recalculate session data

## State Management

The application uses a combination of:
- **React Context**: For global state (authentication)
- **useState Hook**: For component-level state
- **useEffect Hook**: For side effects and lifecycle management
- **localStorage**: For persisting form data and authentication tokens

## Styling Approach

The application uses a combination of:
- **CSS Files**: For global styles
- **Inline Styles**: For component-specific styling
- **CSS-in-JS**: For dynamic styling based on component state

## Data Grid Implementation

The application uses AG Grid for advanced data display:

```javascript
<div className="ag-theme-alpine" style={{ height: '600px', width: '100%' }}>
    <AgGridReact
        rowData={rowData}
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        pagination={true}
        paginationPageSize={15}
        rowGroupPanelShow={'always'}
        enableRangeSelection={true}
        groupDisplayType={'multipleColumns'}
        modules={[ClientSideRowModelModule, RowGroupingModule]}
    />
</div>
```

Features include:
- Pagination
- Sorting
- Filtering
- Row grouping
- Column resizing
- Export to CSV

---

# Deployment Guide

## Local Development

### Prerequisites
- Node.js (v16 or later)
- npm (v7 or later)

### Setup
1. Clone the repository
2. Navigate to the frontend directory
3. Install dependencies:
   ```
   npm install
   ```
4. Create a `.env.local` file with required environment variables:
   ```
   REACT_APP_API_BASE_URL=http://localhost:3001
   ```
5. Start the development server:
   ```
   npm start
   ```
6. Access the application at http://localhost:3000

## Production Deployment

The application is containerized using Docker and deployed to Kubernetes.

### Building the Container

The application uses a multi-stage Dockerfile:
1. Build stage: Compiles the React application
2. Production stage: Serves the built application using Nginx

```dockerfile
# Base image for building the app
FROM node:16-alpine as build

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install

# Copy app source code and build
COPY . ./
RUN npm run build

# Production image with Nginx
FROM nginx:alpine

# Copy build artifacts to Nginx's HTML folder
COPY --from=build /app/build /usr/share/nginx/html

# Create nginx config
RUN rm /etc/nginx/conf.d/default.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80 for the React app (standard HTTP port)
EXPOSE 80

# Start Nginx server
CMD ["nginx", "-g", "daemon off;"]
```

## Environment Variables

### Development Environment
- `REACT_APP_API_BASE_URL`: URL of the backend API

### Production Environment
- `REACT_APP_API_BASE_URL`: URL of the backend API in production

## Container Configuration

The application uses Nginx as the web server in production:

```nginx
server {
    listen 80;
    server_name _;
    root /usr/share/nginx/html;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml;
    gzip_disable "MSIE [1-6]\.";

    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # Static file caching
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, no-transform";
    }
}
```

## Kubernetes Deployment

The application is deployed to Kubernetes using the following manifest:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nse-ops-frontend
  namespace: default
  labels:
    app: nse-ops-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nse-ops-frontend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:
      labels:
        app: nse-ops-frontend
    spec:
      containers:
        - name: nse-ops-frontend
          image: [ECR_REGISTRY_URL]/[IMAGE_NAME]:[TAG]
          ports:
            - containerPort: 80
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 5
      imagePullSecrets:
        - name: aws-registry-secret
```

### Deployment Process

The deployment process is automated through the CI/CD pipeline:
1. Code is pushed to the repository
2. Pipeline builds and tests the application
3. Container image is built and pushed to ECR
4. Kubernetes manifest is updated with the new image tag
5. Updated manifest is uploaded to S3
6. ArgoCD detects the change and updates the deployment

## Monitoring and Logging

The application uses:
- **Logstash**: For log collection and processing
- **Elasticsearch**: For log storage and indexing
- **Kubernetes probes**: For health monitoring

### Logstash Configuration

```
input {
  tcp {
    port => 5044
    codec => json
  }
}

filter {
  if [type] == "syslog" {
    grok {
      match => { "message" => "%{SYSLOGTIMESTAMP:syslog_timestamp} %{SYSLOGHOST:syslog_hostname} %{DATA:syslog_program}(?:\[%{POSINT:syslog_pid}\])?: %{GREEDYDATA:syslog_message}" }
    }
    date {
      match => [ "syslog_timestamp", "MMM  d HH:mm:ss", "MMM dd HH:mm:ss" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    user => "${ELASTICSEARCH_USERNAME}"
    password => "${ELASTICSEARCH_PASSWORD}"
  }
}
```

## Conclusion

This documentation provides a comprehensive guide to the NSE OPS Costing frontend application. By following the instructions in this document, users can effectively use the application, developers can understand and extend its functionality, and operations teams can deploy and maintain it in production.
