# NSE OPS Costing Pipeline Documentation

## Overview

This document provides comprehensive information about the CI/CD pipeline for the NSE OPS Costing application. The pipeline is configured using Bitbucket Pipelines and is defined in the `bitbucket-pipelines.yml` file.

## Pipeline Architecture

The pipeline follows a multi-stage approach to build, test, and deploy the frontend application:

1. **Code Quality Check**: Ensures code quality through linting and testing
2. **Static Code Analysis**: Performs SonarQube analysis for code quality metrics
3. **Build with Buildpacks**: Builds the application container using Cloud Native Buildpacks
4. **Security Scan**: Scans the container image for security vulnerabilities
5. **Push to AWS ECR**: Pushes the container image to Amazon ECR
6. **Update K8s Manifests**: Updates Kubernetes manifests with the new image tag

## Pipeline Configuration

### Environment Variables

The pipeline uses the following environment variables:

| Variable | Description |
|----------|-------------|
| `SONAR_HOST_URL` | URL of the SonarQube server |
| `SONAR_TOKEN` | Authentication token for SonarQube |
| `AWS_REGISTRY_URL` | URL of the AWS ECR registry |
| `IMAGE_NAME` | Name of the container image |
| `AWS_ACCESS_KEY_ID` | AWS access key ID for authentication |
| `AWS_SECRET_ACCESS_KEY` | AWS secret access key for authentication |
| `AWS_REGION` | AWS region where resources are located |
| `ARGOCD_MANIFESTS_BUCKET` | S3 bucket for storing ArgoCD manifests |

### Pipeline Steps

#### 1. Code Quality Check

```yaml
- step:
    name: "Code Quality Check"
    script:
      - npm install
      - npm run lint
      - npm test
```

This step:
- Installs Node.js dependencies
- Runs linting to check code style and quality
- Executes unit tests to verify functionality

#### 2. Static Code Analysis

```yaml
- step: *sonarqube-analysis
```

This step uses the SonarQube pipe to perform static code analysis:
- Analyzes code quality
- Identifies code smells, bugs, and vulnerabilities
- Provides code coverage metrics

#### 3. Build with Buildpacks

```yaml
- step:
    name: "Build with Buildpacks"
    services:
      - docker
    script:
      - pipe: docker://buildpacks/pack:latest
        variables:
          BUILDER: 'paketobuildpacks/builder:base'
          IMAGE: $AWS_REGISTRY_URL/$IMAGE_NAME:$BITBUCKET_COMMIT
          PATH: './frontend/frontend'
```

This step:
- Uses Cloud Native Buildpacks to build the container image
- Tags the image with the Bitbucket commit hash
- Prepares the image for deployment

#### 4. Security Scan

```yaml
- step: *security-scan
```

This step uses Trivy to scan the container image for security vulnerabilities:
- Identifies critical and high severity vulnerabilities
- Ensures the container image is secure before deployment

#### 5. Push to AWS ECR

```yaml
- step:
    name: "Push to AWS ECR"
    services:
      - docker
    script:
      - pipe: atlassian/aws-ecr-push-image:1.5.0
        variables:
          AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
          AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
          AWS_DEFAULT_REGION: $AWS_REGION
          IMAGE_NAME: $IMAGE_NAME
          TAGS: $BITBUCKET_COMMIT
```

This step:
- Authenticates with AWS ECR
- Pushes the container image to the ECR repository
- Tags the image with the Bitbucket commit hash

#### 6. Update K8s Manifests

```yaml
- step:
    name: "Update K8s Manifests"
    script:
      - sed -i "s|image:.*|image: $AWS_REGISTRY_URL/$IMAGE_NAME:$BITBUCKET_COMMIT|" frontend/frontend/deployment.yaml
      - pipe: atlassian/aws-s3-deploy:1.1.0
        variables:
          AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
          AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
          AWS_DEFAULT_REGION: $AWS_REGION
          S3_BUCKET: $ARGOCD_MANIFESTS_BUCKET
          LOCAL_PATH: 'frontend/frontend/deployment.yaml'
```

This step:
- Updates the Kubernetes deployment manifest with the new image tag
- Uploads the updated manifest to an S3 bucket
- Enables GitOps-based deployment through ArgoCD

## Reusable Step Definitions

The pipeline uses reusable step definitions to avoid duplication:

### SonarQube Analysis

```yaml
- step: &sonarqube-analysis
    name: "Static Code Analysis"
    script:
      - pipe: sonarsource/sonarqube-scan:1.0.0
        variables:
          SONAR_HOST_URL: ${SONAR_HOST_URL}
          SONAR_TOKEN: ${SONAR_TOKEN}
```

### Security Scan

```yaml
- step: &security-scan
    name: "Security Scan"
    script:
      - pipe: aquasecurity/trivy-pipe:1.0.0
        variables:
          IMAGE: $AWS_REGISTRY_URL/$IMAGE_NAME:$BITBUCKET_COMMIT
          SEVERITY: 'CRITICAL,HIGH'
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check that all dependencies are correctly specified in package.json
   - Verify that all environment variables are correctly set in Bitbucket

2. **Test Failures**
   - Review test logs to identify failing tests
   - Fix failing tests before pushing changes

3. **Deployment Failures**
   - Verify AWS credentials are correctly set
   - Check that the ECR repository exists and is accessible
   - Ensure the S3 bucket for ArgoCD manifests exists and is accessible

## Best Practices

1. **Version Control**
   - Make small, incremental changes
   - Use descriptive commit messages
   - Create feature branches for new features

2. **Testing**
   - Write comprehensive unit tests
   - Run tests locally before pushing changes
   - Maintain high code coverage

3. **Security**
   - Regularly update dependencies
   - Address security vulnerabilities promptly
   - Use secrets management for sensitive information

## Conclusion

This pipeline provides a robust CI/CD workflow for the NSE OPS Costing frontend application. By following the steps outlined in this document, you can ensure that your code is properly tested, built, and deployed to production.
