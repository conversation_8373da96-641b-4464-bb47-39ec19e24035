# Base image for building the app
FROM node:16-alpine as build

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install

# Copy app source code and build
COPY . ./
RUN npm run build

# Production image with Nginx
FROM nginx:alpine

# Copy build artifacts to Nginx's HTML folder
COPY --from=build /app/build /usr/share/nginx/html

# Create nginx config
RUN rm /etc/nginx/conf.d/default.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80 for the React app (standard HTTP port)
EXPOSE 80

# Start Nginx server
CMD ["nginx", "-g", "daemon off;"]
