{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@ag-grid-community/client-side-row-model": "^32.3.3", "@ag-grid-community/core": "^32.3.3", "@ag-grid-community/react": "^32.3.3", "@ag-grid-community/styles": "^33.0.3", "@ag-grid-enterprise/all-modules": "^27.3.0", "@ag-grid-enterprise/row-grouping": "^32.3.3", "@elastic/elasticsearch": "^8.12.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "ag-grid-enterprise": "^33.0.4", "ag-grid-react": "^33.0.4", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "dotenv": "^16.4.7", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.10.0", "react-datepicker": "^7.6.0", "react-router-dom": "^7.1.3", "react-scripts": "^5.0.1", "react-select": "^5.9.0", "useragent": "^2.3.0", "web-vitals": "^4.2.4", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}