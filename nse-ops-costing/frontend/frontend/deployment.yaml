apiVersion: apps/v1
kind: Deployment
metadata:
  name: nse-ops-frontend
  namespace: default
  labels:
    app: nse-ops-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nse-ops-frontend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:
      labels:
        app: nse-ops-frontend
    spec:
      containers:
        - name: nse-ops-frontend
          image: abhinav173/cicdimages:latest 
          ports:
            - containerPort: 80
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 5
      imagePullSecrets:
        - name: aws-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: nse-ops-frontend-service
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    nodePort: 30000
  selector:
    app: nse-ops-frontend
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nse-ops-frontend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nse-ops-frontend
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
