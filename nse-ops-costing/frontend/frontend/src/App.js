import React from 'react';
import { BrowserRouter as Router, Routes, Route, Outlet } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Login from './Login';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import EditSessionsPage from './EditSessionsPage';
import InputPage from './InputPage';
import Header from './Header';
import OpsPriceGrid from './OpsPriceGrid';
import './App.css';

// Layout component to wrap protected routes with Header
const ProtectedLayout = () => {
    return (
        <>
            <Header />
            <Outlet />
        </>
    );
};

function App() {
    return (
        <Router>
            <AuthProvider>
                <Routes>
                    {/* Public routes */}
                    <Route path="/login" element={<Login />} />
                    <Route path="/forgot-password" element={<ForgotPassword />} />
                    <Route path="/reset-password" element={<ResetPassword />} />

                    {/* Protected routes */}
                    <Route element={<ProtectedRoute />}>
                        <Route element={<ProtectedLayout />}>
                            <Route path="/" element={<InputPage />} />
                            <Route path="/input" element={<InputPage />} />
                            <Route path="/ops-price" element={<OpsPriceGrid />} />
                            <Route path="/edit-sessions" element={<EditSessionsPage />} />
                        </Route>
                    </Route>

                    {/* Admin routes */}
                    <Route element={<ProtectedRoute roles={['admin']} />}>
                        <Route path="/admin" element={
                            <>
                                <Header />
                                <OpsPriceGrid />
                            </>
                        } />
                    </Route>

                    {/* 404 route */}
                    <Route path="*" element={<div>404 - Page Not Found</div>} />
                </Routes>
            </AuthProvider>
        </Router>
    );
}

export default App;