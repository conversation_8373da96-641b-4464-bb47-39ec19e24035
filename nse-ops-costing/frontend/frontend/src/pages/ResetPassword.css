.reset-password-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
}

.reset-password-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.reset-password-card h2 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
}

.subtitle {
    text-align: center;
    color: #666;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.reset-password-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    color: #2c3e50;
    font-weight: 500;
}

.form-group input {
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.submit-button {
    background-color: #3498db;
    color: white;
    padding: 0.8rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.submit-button:hover {
    background-color: #2980b9;
}

.submit-button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

.back-button {
    background-color: transparent;
    color: #3498db;
    padding: 0.8rem;
    border: 1px solid #3498db;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-button:hover {
    background-color: #f8f9fa;
}

.success-message {
    color: #27ae60;
    text-align: center;
    padding: 0.5rem;
    background-color: #e8f5e9;
    border-radius: 5px;
    font-size: 0.9rem;
}

.error-message {
    color: #e74c3c;
    text-align: center;
    padding: 0.5rem;
    background-color: #fde8e8;
    border-radius: 5px;
    font-size: 0.9rem;
} 