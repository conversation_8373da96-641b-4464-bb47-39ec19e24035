import React, { useState, useEffect } from 'react';
import CreatableSelect from 'react-select/creatable';
import { fetchWithAuth } from './utils/api';
import LoadingSpinner from './components/LoadingSpinner';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';

const apiBaseUrl = process.env.REACT_APP_API_BASE_URL;

const EditSessionsPage = () => {
    const [sessions, setSessions] = useState([]);
    const [error, setError] = useState('');
    const [filters, setFilters] = useState({
        traderID: null,
        clientID: null,
        rackID: null,
        firmName: null,
        tapIP: null,
        activeOnly: false
    });
    const [options, setOptions] = useState({
        traderOptions: [],
        clientOptions: [],
        rackOptions: [],
        firmOptions: [],
        tapOptions: []
    });
    const [editingState, setEditingState] = useState({
        rowKey: null,
        field: null,
        value: null,
        originalSession: null
    });
    const [isLoading, setIsLoading] = useState(false);

    // Fetch initial Trader IDs and Client IDs
    useEffect(() => {
        // Fetch all trader IDs first
        fetchWithAuth('/api/get-trader-ids')
            .then(response => response.json())
            .then(data => {
                setOptions(prev => ({
                    ...prev,
                    traderOptions: data.trader_ids ? data.trader_ids.map(id => ({ value: id, label: id })) : []
                }));
            })
            .catch(error => {
                console.error('Error fetching Trader IDs:', error);
                setError('Failed to fetch Trader IDs');
            });
    }, []);

    // Fetch Client IDs when Trader ID changes
    useEffect(() => {
        if (filters.traderID) {
            fetchWithAuth(`/api/get-client-ids/${filters.traderID.value}`)
                .then(response => response.json())
                .then(data => {
                    setOptions(prev => ({
                        ...prev,
                        clientOptions: data.client_ids ? data.client_ids.map(id => ({ value: id, label: id })) : []
                    }));
                })
                .catch(error => {
                    console.error('Error fetching Client IDs:', error);
                    setError('Failed to fetch Client IDs');
                });
        } else {
            setOptions(prev => ({ ...prev, clientOptions: [] }));
        }
    }, [filters.traderID]);

    // Fetch Rack IDs when Client ID or Trader ID changes
    useEffect(() => {
        if (filters.clientID && filters.traderID) {
            fetchWithAuth(`/api/get-rack-ids/${filters.clientID.value}/${filters.traderID.value}`)
                .then(response => response.json())
                .then(data => {
                    if (data.rack_ids) {
                        setOptions({
                            ...options,
                            rackOptions: data.rack_ids.map(id => ({ value: id, label: id }))
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching Rack IDs:', error);
                    setError('Failed to fetch Rack IDs');
                });
        }
    }, [filters.clientID, filters.traderID]);

    // Fetch Firm Names when Client ID, Trader ID, or Rack ID changes
    useEffect(() => {
        if (filters.clientID && filters.traderID && filters.rackID) {
            fetchWithAuth(`/api/get-firm-names/${filters.clientID.value}/${filters.traderID.value}/${filters.rackID.value}`)
                .then(response => response.json())
                .then(data => {
                    if (data.firm_names) {
                        setOptions({
                            ...options,
                            firmOptions: data.firm_names.map(name => ({ value: name, label: name }))
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching Firm Names:', error);
                    setError('Failed to fetch Firm Names');
                });
        }
    }, [filters.clientID, filters.traderID, filters.rackID]);

    // Fetch Tap IPs when Client ID, Trader ID, Rack ID, or Firm Name changes
    useEffect(() => {
        if (filters.clientID && filters.traderID && filters.rackID && filters.firmName) {
            fetchWithAuth(`/api/get-tap-ips/${filters.clientID.value}/${filters.traderID.value}/${filters.rackID.value}/${filters.firmName.value}`)
                .then(response => response.json())
                .then(data => {
                    if (data.tap_ips) {
                        setOptions({
                            ...options,
                            tapOptions: data.tap_ips.map(ip => ({ value: ip, label: ip }))
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching Tap IPs:', error);
                    setError('Failed to fetch Tap IPs');
                });
        }
    }, [filters.clientID, filters.traderID, filters.rackID, filters.firmName]);

    // Function to fetch sessions based on filters
    const fetchSessions = () => {
        setIsLoading(true);
        setError('');
        
        const queryParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
            if (value) {
                if (typeof value === 'object' && value.value) {
                    queryParams.append(key, value.value);
                } else {
                    queryParams.append(key, value);
                }
            }
        });

        fetchWithAuth(`/api/fetch-sessions?${queryParams}`)
            .then(response => response.json())
            .then(data => {
                setSessions(data);
                setIsLoading(false);
            })
            .catch(error => {
                console.error('Error fetching sessions:', error);
                setError('Failed to fetch sessions');
                setIsLoading(false);
            });
    };

    // Handle updating session details
    const handleUpdateSession = async (sessionData, originalSession) => {
        setIsLoading(true);
        
        try {
            const response = await fetchWithAuth('/api/update-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    updatedData: sessionData,
                    originalData: originalSession
                }),
            });
            const data = await response.json();
            if (data.status === 'success') {
                alert('Session updated successfully!');
                setEditingState({ rowKey: null, field: null, value: null, originalSession: null });
                fetchSessions();
            } else {
                throw new Error(data.message || 'Failed to update session');
            }
        } catch (error) {
            console.error('Error updating session:', error);
            setError(error.message || 'Failed to update session');
            setEditingState({ rowKey: null, field: null, value: null, originalSession: null });
        } finally {
            setIsLoading(false);
        }
    };

    // Function to handle field edit
    const handleFieldEdit = (session, field, index) => {
        // If clicking the same cell that's already being edited, cancel editing
        const rowKey = `${session.Trader_ID}-${session.Session_ID}-${index}`;
        if (editingState.rowKey === rowKey && editingState.field === field) {
            handleFieldCancel();
            return;
        }

        // Format date values for editing
        let value = session[field];
        if (field === 'Start_Date' || field === 'END_DATE') {
            if (value) {
                // Extract YYYY-MM-DD from the date string
                value = value.split('T')[0];
            }
        }

        // Format original session dates
        const formattedOriginalSession = {
            ...session,
            Start_Date: session.Start_Date ? session.Start_Date.split('T')[0] : null,
            END_DATE: session.END_DATE ? session.END_DATE.split('T')[0] : null
        };

        // Start editing the new cell
        setEditingState({
            rowKey,
            field,
            value: value,
            originalSession: formattedOriginalSession
        });
    };

    // Function to handle field save
    const handleFieldSave = (session) => {
        // Create updated session with only the changed field
        const updatedSession = {
            ...editingState.originalSession,  // Start with the original session data
            [editingState.field]: editingState.value  // Only update the changed field
        };

        // Format dates before sending to API
        if (editingState.field === 'Start_Date' || editingState.field === 'END_DATE') {
            if (updatedSession[editingState.field]) {
                // Validate YYYY-MM-DD format
                const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                if (!dateRegex.test(updatedSession[editingState.field])) {
                    setError('Invalid date format. Please use YYYY-MM-DD');
                    return;
                }
                // Keep the date in YYYY-MM-DD format
                updatedSession[editingState.field] = updatedSession[editingState.field];
            }
        }

        // Format original session dates
        const formattedOriginalSession = {
            ...editingState.originalSession,
            Start_Date: editingState.originalSession.Start_Date ? editingState.originalSession.Start_Date.split('T')[0] : null,
            END_DATE: editingState.originalSession.END_DATE ? editingState.originalSession.END_DATE.split('T')[0] : null
        };

        handleUpdateSession(updatedSession, formattedOriginalSession);
        setEditingState({ rowKey: null, field: null, value: null, originalSession: null });
    };

    // Function to handle field cancel
    const handleFieldCancel = () => {
        setEditingState({ rowKey: null, field: null, value: null, originalSession: null });
    };

    // Function to handle input change
    const handleInputChange = (e) => {
        let newValue = e.target.value;
        
        if (e.target.type === 'number') {
            newValue = e.target.value ? parseInt(e.target.value) : null;
        } else if (e.target.type === 'text' && (editingState.field === 'Start_Date' || editingState.field === 'END_DATE')) {
            // Allow any input but validate before saving
            setEditingState(prev => ({
                ...prev,
                value: newValue
            }));
            return;
        }
        
        setEditingState(prev => ({
            ...prev,
            value: newValue
        }));
    };

    const renderCell = (session, field, value, index) => {
        const rowKey = `${session.Trader_ID}-${session.Session_ID}-${index}`;
        const isEditingThisCell = editingState.rowKey === rowKey && editingState.field === field;
    
        if (isEditingThisCell) {
            return (
                <div style={{ display: 'flex', gap: '5px' }}>
                    {(field === 'Start_Date' || field === 'END_DATE') ? (
                        <input
                            type="date"
                            value={editingState.value || ''}
                            onChange={handleInputChange}
                            onClick={e => e.stopPropagation()}
                            style={{ width: '140px' }}
                            autoFocus
                        />
                    ) : field === 'ops_per_second' || field === 'FO_OPS' || field === 'CM_OPS' ? (
                        <input
                            type="number"
                            value={editingState.value || ''}
                            onChange={handleInputChange}
                            onClick={e => e.stopPropagation()}
                            style={{ width: '80px' }}
                            autoFocus
                        />
                    ) : field === 'LinkType' ? (
                        <div style={{ display: 'flex', gap: '10px' }} onClick={e => e.stopPropagation()}>
                            <label>
                                <input
                                    type="radio"
                                    name="linkTypeEdit"
                                    value="Direct"
                                    checked={editingState.value === 'Direct'}
                                    onChange={() => setEditingState(prev => ({ ...prev, value: 'Direct' }))}
                                />
                                Direct
                            </label>
                            <label>
                                <input
                                    type="radio"
                                    name="linkTypeEdit"
                                    value="Switch"
                                    checked={editingState.value === 'Switch'}
                                    onChange={() => setEditingState(prev => ({ ...prev, value: 'Switch' }))}
                                />
                                Switch
                            </label>
                            <button onClick={() => handleFieldSave(session)} style={{ marginLeft: '10px' }}>✓</button>
                            <button onClick={handleFieldCancel} style={{ marginLeft: '5px' }}>✕</button>
                        </div>
                    ) : (
                        <input
                            type="text"
                            value={editingState.value || ''}
                            onChange={handleInputChange}
                            onClick={e => e.stopPropagation()}
                            style={{ width: '120px' }}
                            autoFocus
                        />
                    )}
                    {(field !== 'LinkType') && (
                        <>
                            <button 
                                onClick={e => { e.stopPropagation(); handleFieldSave(session); }} 
                                style={{ 
                                    padding: '2px 8px',
                                    backgroundColor: '#28a745',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer',
                                    fontSize: '16px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '32px',
                                    height: '32px'
                                }}
                                title="Save"
                            >
                                ✓
                            </button>
                            <button 
                                onClick={e => { e.stopPropagation(); handleFieldCancel(); }} 
                                style={{ 
                                    padding: '2px 8px',
                                    backgroundColor: '#dc3545',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer',
                                    fontSize: '16px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '32px',
                                    height: '32px'
                                }}
                                title="Cancel"
                            >
                                ✕
                            </button>
                        </>
                    )}
                </div>
            );
        }
    
        // For non-editing mode, format the display value
        if (field === 'Start_Date' || field === 'END_DATE') {
            if (value) {
                // Display date in YYYY-MM-DD format
                return value.split('T')[0];
            }
            return '';
        }
        // Handle numeric fields
        if (field === 'ops_per_second' || field === 'FO_OPS' || field === 'CM_OPS') {
            return value !== null && value !== undefined ? value.toString() : '0';
        }
        return value || '';
    };
    
    // Function to handle session deletion
    const handleDeleteSession = async (session) => {
        if (!window.confirm('Are you sure you want to delete this session?')) return;

        setIsLoading(true);
        try {
            // Format dates before sending to API
            const formattedSession = {
                ...session,
                Start_Date: session.Start_Date ? session.Start_Date.split('T')[0] : null,
                END_DATE: session.END_DATE ? session.END_DATE.split('T')[0] : null
            };

            const response = await fetchWithAuth('/api/delete-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formattedSession),
            });
            const data = await response.json();
            if (data.status === 'success') {
                alert('Session deleted successfully!');
                fetchSessions();
            } else {
                throw new Error(data.message || 'Failed to delete session');
            }
        } catch (error) {
            console.error('Error deleting session:', error);
            setError(error.message || 'Failed to delete session');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div>
            {isLoading && <LoadingSpinner />}
            <h2>Edit Sessions Page</h2>
            {error && <div style={{ color: 'red' }}>{error}</div>}
            
            {/* Filter Section */}
            <div style={{ marginBottom: '20px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
                <h3>Filters</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', gap: '10px' }}>
                    <div>
                        <label>Trader ID:</label>
                        <CreatableSelect
                            isClearable
                            options={options.traderOptions}
                            value={filters.traderID}
                            onChange={(selected) => setFilters({ ...filters, traderID: selected })}
                            placeholder="Select Trader ID"
                        />
                    </div>
                    <div>
                        <label>Client ID:</label>
                        <CreatableSelect
                            isClearable
                            options={options.clientOptions}
                            value={filters.clientID}
                            onChange={(selected) => setFilters({ ...filters, clientID: selected })}
                            placeholder="Select Client ID"
                        />
                    </div>
                    <div>
                        <label>Rack ID:</label>
                        <CreatableSelect
                            isClearable
                            options={options.rackOptions}
                            value={filters.rackID}
                            onChange={(selected) => setFilters({ ...filters, rackID: selected })}
                            placeholder="Select Rack ID"
                            isDisabled={!filters.traderID}
                        />
                    </div>
                    <div>
                        <label>Firm Name:</label>
                        <CreatableSelect
                            isClearable
                            options={options.firmOptions}
                            value={filters.firmName}
                            onChange={(selected) => setFilters({ ...filters, firmName: selected })}
                            placeholder="Select Firm Name"
                            isDisabled={!filters.rackID}
                        />
                    </div>
                    <div>
                        <label>Tap IP:</label>
                        <CreatableSelect
                            isClearable
                            options={options.tapOptions}
                            value={filters.tapIP}
                            onChange={(selected) => setFilters({ ...filters, tapIP: selected })}
                            placeholder="Select Tap IP"
                            isDisabled={!filters.firmName}
                        />
                    </div>
                    <div>
                        <label>
                            <input
                                type="checkbox"
                                checked={filters.activeOnly}
                                onChange={(e) => setFilters({ ...filters, activeOnly: e.target.checked })}
                            />
                            Show Active Sessions Only
                        </label>
                    </div>
                </div>
                <div style={{ marginTop: '20px', textAlign: 'right' }}>
                    <button 
                        onClick={fetchSessions}
                        disabled={isLoading}
                        style={{
                            padding: '8px 16px',
                            backgroundColor: '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: isLoading ? 'not-allowed' : 'pointer',
                            opacity: isLoading ? 0.7 : 1,
                            fontSize: '14px',
                            width: 'auto',
                            minWidth: '120px'
                        }}
                    >
                        {isLoading ? 'Fetching...' : 'Fetch Sessions'}
                    </button>
                </div>
            </div>

            {/* Sessions Table */}
            {sessions.length > 0 && (
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                    <thead>
                        <tr>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Trader ID</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Client ID</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Rack ID</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Firm Name</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Tap IP</th>                
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Operations per Session</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Start Date</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>FO_OPS</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>CM_OPS</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>End Date</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Link Type</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {sessions.map((session, index) => (
                            <tr key={`${session.Trader_ID}-${session.Session_ID}-${index}`}>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'Trader_ID', index)}>
                                    {renderCell(session, 'Trader_ID', session.Trader_ID, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'Client_ID', index)}>
                                    {renderCell(session, 'Client_ID', session.Client_ID, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'Rack_ID', index)}>
                                    {renderCell(session, 'Rack_ID', session.Rack_ID, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'Firm_Name', index)}>
                                    {renderCell(session, 'Firm_Name', session.Firm_Name, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'Tap_IP', index)}>
                                    {renderCell(session, 'Tap_IP', session.Tap_IP, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'ops_per_second', index)}>
                                    {renderCell(session, 'ops_per_second', session.ops_per_second, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'Start_Date', index)}>
                                    {renderCell(session, 'Start_Date', session.Start_Date, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'FO_OPS', index)}>
                                    {renderCell(session, 'FO_OPS', session.FO_OPS, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'CM_OPS', index)}>
                                    {renderCell(session, 'CM_OPS', session.CM_OPS, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'END_DATE', index)}>
                                    {renderCell(session, 'END_DATE', session.END_DATE, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px', cursor: 'pointer' }}
                                    onClick={() => handleFieldEdit(session, 'LinkType', index)}>
                                    {renderCell(session, 'LinkType', session.LinkType, index)}
                                </td>
                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                                    {/* Actions cell: show delete icon */}
                                    <button 
                                        onClick={() => handleDeleteSession(session)}
                                        style={{ 
                                            padding: '4px 8px',
                                            backgroundColor: '#dc3545',
                                            color: 'white',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer',
                                            fontSize: '16px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            width: '32px',
                                            height: '32px',
                                            margin: '0 auto'
                                        }}
                                        title="Delete"
                                    >
                                        <FontAwesomeIcon icon={faTrash} />
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            )}
        </div>
    );
};

export default EditSessionsPage;