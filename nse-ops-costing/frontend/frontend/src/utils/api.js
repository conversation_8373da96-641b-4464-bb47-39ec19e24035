export const fetchWithAuth = async (endpoint, options = {}) => {
    const token = localStorage.getItem('accessToken');
    const username = localStorage.getItem('username');
    
    if (!token) {
        throw new Error('No authentication token found');
    }

    const headers = {
        'Authorization': `Bearer ${token}`,
        'X-Username': username,
        'Content-Type': 'application/json',
        ...options.headers
    };

    try {
        const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}${endpoint}`, {
            ...options,
            headers
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response;
    } catch (error) {
        // Keep error logging for debugging API issues
        console.error('API call failed:', error);
        throw error;
    }
}; 