import React, { useState, useEffect } from 'react';
import CreatableSelect from 'react-select/creatable';
import { fetchWithAuth } from './utils/api';
import LoadingSpinner from './components/LoadingSpinner';

const apiBaseUrl = process.env.REACT_APP_API_BASE_URL;

const TraderSessionForm = ({ onSubmit, error, setError }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [clientID, setClientID] = useState([]); // New state for Client ID
    const [traderID, setTraderID] = useState([]);
    const [rackID, setRackID] = useState('');
    const [firmName, setFirmName] = useState('');
    const [tapIP, setTapIP] = useState('');
    const [opsPerSecond, setOpsPerSecond] = useState(40);
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [foOps, setFoOps] = useState(40);
    const [cmOps, setCmOps] = useState(0);
    const [linkType, setLinkType] = useState('Direct'); // New state for Link Type
    const [options, setOptions] = useState({
        traderOptions: [],
        rackOptions: [],
        firmOptions: [],
        tapOptions: [],
        clientOptions: [] // New options state for Client IDs
    });

    // Update CM_OPS when FO_OPS changes to maintain FO_OPS + CM_OPS = ops_per_second
    useEffect(() => {
        setCmOps(opsPerSecond - foOps);
    }, [foOps, opsPerSecond]);

    // Update FO_OPS when CM_OPS changes to maintain FO_OPS + CM_OPS = ops_per_second
    useEffect(() => {
        setFoOps(opsPerSecond - cmOps);
    }, [cmOps, opsPerSecond]);

    // Fetch all trader IDs on mount
    useEffect(() => {
        fetchWithAuth('/api/get-trader-ids')
            .then(response => response.json())
            .then(data => {
                setOptions(prev => ({
                    ...prev,
                    traderOptions: data.trader_ids ? data.trader_ids.map(id => ({ value: id, label: id })) : []
                }));
            })
            .catch((error) => {
                console.error('Error fetching Trader IDs:', error);
                if (setError) setError('Failed to fetch Trader IDs');
            });
    }, [setError]);

    // Fetch client IDs when traderID changes
    useEffect(() => {
        if (traderID.length > 0) {
            // Only use the first selected traderID for client lookup
            fetchWithAuth(`/api/get-client-ids/${traderID[0].value}`)
                .then(response => response.json())
                .then(data => {
                    setOptions(prev => ({
                        ...prev,
                        clientOptions: data.client_ids ? data.client_ids.map(id => ({ value: id, label: id })) : []
                    }));
                })
                .catch((error) => {
                    console.error('Error fetching Client IDs:', error);
                    if (setError) setError('Failed to fetch Client IDs');
                });
        } else {
            setOptions(prev => ({ ...prev, clientOptions: [] }));
        }
    }, [traderID, setError]);

    // Fetch Rack IDs when Client ID or Trader ID changes
    useEffect(() => {
        if (clientID.length > 0 && traderID.length > 0) {
            setIsLoading(true);
            fetchWithAuth(`/api/get-rack-ids/${clientID[0].value}/${traderID[0].value}`)
                .then(response => response.json())
                .then(data => {
                    if (data.rack_ids) {
                        setOptions(prev => ({
                            ...prev,
                            rackOptions: data.rack_ids.map(id => ({ value: id, label: id }))
                        }));
                    }
                })
                .catch((error) => {
                    console.error('Error fetching Rack IDs:', error);
                    if (setError) setError('Failed to fetch Rack IDs');
                })
                .finally(() => setIsLoading(false));
        } else {
            setOptions(prev => ({
                ...prev,
                rackOptions: []
            }));
        }
    }, [clientID, traderID, setError]);

    // Fetch Firm Names when Rack ID, Client ID, or Trader ID changes
    useEffect(() => {
        if (rackID && clientID.length > 0 && traderID.length > 0) {
            setIsLoading(true);
            fetchWithAuth(`/api/get-firm-names/${clientID[0].value}/${traderID[0].value}/${rackID}`)
                .then(response => response.json())
                .then(data => {
                    if (data.firm_names) {
                        setOptions(prev => ({
                            ...prev,
                            firmOptions: data.firm_names.map(name => ({ value: name, label: name }))
                        }));
                    }
                })
                .catch((error) => {
                    console.error('Error fetching Firm Names:', error);
                    if (setError) setError('Failed to fetch Firm Names');
                })
                .finally(() => setIsLoading(false));
        } else {
            setOptions(prev => ({
                ...prev,
                firmOptions: []
            }));
        }
    }, [rackID, clientID, traderID, setError]);

    // Fetch Tap IPs when Firm Name, Rack ID, Client ID, or Trader ID changes
    useEffect(() => {
        if (firmName && rackID && clientID.length > 0 && traderID.length > 0) {
            setIsLoading(true);
            fetchWithAuth(`/api/get-tap-ips/${clientID[0].value}/${traderID[0].value}/${rackID}/${firmName}`)
                .then(response => response.json())
                .then(data => {
                    if (data.tap_ips) {
                        setOptions(prev => ({
                            ...prev,
                            tapOptions: data.tap_ips.map(ip => ({ value: ip, label: ip }))
                        }));
                    }
                })
                .catch((error) => {
                    console.error('Error fetching Tap IPs:', error);
                    if (setError) setError('Failed to fetch Tap IPs');
                })
                .finally(() => setIsLoading(false));
        } else {
            setOptions(prev => ({
                ...prev,
                tapOptions: []
            }));
        }
    }, [firmName, rackID, clientID, traderID, setError]);

    const handleSubmit = (e) => {
        e.preventDefault();
        setIsLoading(true);

        // Validate FO_OPS + CM_OPS = ops_per_second
        if (foOps + cmOps !== opsPerSecond) {
            if (setError) {
                setError('FO_OPS + CM_OPS must equal ops_per_second.');
            }
            setIsLoading(false);
            return;
        }

        // Clear error if validation passes
        if (setError) {
            setError('');
        }

        // Prepare form data
        const formData = {
            clientID: clientID.map(c => c.value).join('|'),
            traderID: traderID.map(t => t.value).join('|'),
            rackID,
            firmName,
            tapIP,
            opsPerSecond,
            startDate,
            endDate,
            foOps,
            cmOps,
            linkType, // Include linkType in form data
        };

        // Make API call to submit form
        fetchWithAuth('/api/submit-form', {
            method: 'POST',
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Show success popup
                alert('Form submitted successfully!');
                // Pass form data to parent component after successful submission
                if (onSubmit && typeof onSubmit === 'function') {
                    onSubmit(formData);
                }
                // Clear form after successful submission
                setClientID([]);
                setTraderID([]);
                setRackID('');
                setFirmName('');
                setTapIP('');
                setOpsPerSecond(40);
                setStartDate('');
                setEndDate('');
                setFoOps(40);
                setCmOps(0);
                setLinkType('Direct'); // Reset linkType to default
            } else {
                throw new Error('Failed to submit form');
            }
        })
        .catch(error => {
            console.error('Error submitting form:', error);
            alert('Failed to submit form. Please try again.');
            if (setError) {
                setError('Failed to submit form. Please try again.');
            }
        })
        .finally(() => {
            setIsLoading(false);
        });
    };

    return (
        <form onSubmit={handleSubmit} style={{ position: 'relative' }}>
            {isLoading && (
                <div style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    zIndex: 1,
                    pointerEvents: 'none'
                }}>
                    <LoadingSpinner />
                </div>
            )}
            <div style={{
                opacity: isLoading ? 0.5 : 1,
                pointerEvents: isLoading ? 'none' : 'auto'
            }}>
                {/* Trader ID (Multi-Select Auto-Complete with Creatable) */}
                <div>
                    <label>Trader ID: </label>
                    <CreatableSelect
                        isMulti
                        options={options.traderOptions}
                        value={traderID}
                        onChange={(selectedOptions) => setTraderID(selectedOptions || [])}
                        placeholder="Select or type Trader IDs"
                        isClearable
                        formatCreateLabel={(inputValue) => `Add "${inputValue}"`}
                        required
                        style={{ width: '100%' }}
                    />
                </div>

                {/* Client ID (Multi-Select Auto-Complete with Creatable) */}
                <div>
                    <label>Client ID: </label>
                    <CreatableSelect
                        isMulti
                        options={options.clientOptions}
                        value={clientID}
                        onChange={(selectedOptions) => setClientID(selectedOptions || [])}
                        placeholder="Select or type Client IDs"
                        isClearable
                        formatCreateLabel={(inputValue) => `Add \"${inputValue}\"`}
                        required
                        style={{ width: '100%' }}
                    />
                </div>

                {/* Rack ID (Auto-Complete with Creatable) */}
                <div>
                    <label>Rack ID: </label>
                    <CreatableSelect
                        options={options.rackOptions}
                        value={options.rackOptions.find(opt => opt.value === rackID) || { value: rackID, label: rackID }}
                        onChange={(selectedOption) => setRackID(selectedOption ? selectedOption.value : '')}
                        placeholder="Select or type Rack ID"
                        isClearable
                        formatCreateLabel={(inputValue) => `Add "${inputValue}"`}
                        required
                        style={{ width: '50%' }}
                        isDisabled={!traderID.length}
                    />
                </div>

                {/* Firm Name (Auto-Complete with Creatable) */}
                <div>
                    <label>Firm Name: </label>
                    <CreatableSelect
                        options={options.firmOptions}
                        value={options.firmOptions.find(opt => opt.value === firmName) || { value: firmName, label: firmName }}
                        onChange={(selectedOption) => setFirmName(selectedOption ? selectedOption.value : '')}
                        placeholder="Select or type Firm Name"
                        isClearable
                        formatCreateLabel={(inputValue) => `Add "${inputValue}"`}
                        required
                        style={{ width: '50%' }}
                        isDisabled={!rackID}
                    />
                </div>

                {/* Tap IP (Auto-Complete with Creatable) */}
                <div>
                    <label>Tap IP: </label>
                    <CreatableSelect
                        options={options.tapOptions}
                        value={options.tapOptions.find(opt => opt.value === tapIP) || { value: tapIP, label: tapIP }}
                        onChange={(selectedOption) => setTapIP(selectedOption ? selectedOption.value : '')}
                        placeholder="Select or type Tap IP"
                        isClearable
                        formatCreateLabel={(inputValue) => `Add "${inputValue}"`}
                        required
                        style={{ width: '50%' }}
                        isDisabled={!firmName}
                    />
                </div>

                {/* OPS per Second (Dropdown) */}
                <div>
                    <label>OPS per Second: </label>
                    <select
                        value={opsPerSecond}
                        onChange={(e) => {
                            const newOps = parseInt(e.target.value);
                            setOpsPerSecond(newOps);
                            // Update FO_OPS to maintain the sum
                            setFoOps(newOps - cmOps);
                        }}
                        required
                        style={{ width: '25%' }}
                    >
                        <option value={40}>40</option>
                        <option value={100}>100</option>
                        <option value={200}>200</option>
                        <option value={400}>400</option>
                        <option value={1000}>1000</option>
                    </select>
                </div>

                {/* Start Date */}
                <div>
                    <label>Start Date: </label>
                    <input
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        required
                        style={{ width: '25%' }} // Reduced width
                    />
                </div>

                {/* End Date */}
                <div>
                    <label>End Date: </label>
                    <input
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                        style={{ width: '25%' }} // Reduced width
                    />
                </div>

                {/* FO_OPS */}
                <div>
                    <label>FO_OPS: </label>
                    <input
                        type="number"
                        value={foOps}
                        onChange={(e) => {
                            const newFoOps = parseInt(e.target.value) || 0;
                            setFoOps(newFoOps);
                            // Update CM_OPS to maintain the sum
                            setCmOps(opsPerSecond - newFoOps);
                        }}
                        required
                        style={{ width: '25%' }}
                    />
                </div>

                {/* CM_OPS */}
                <div>
                    <label>CM_OPS: </label>
                    <input
                        type="number"
                        value={cmOps}
                        onChange={(e) => {
                            const newCmOps = parseInt(e.target.value) || 0;
                            setCmOps(newCmOps);
                            // Update FO_OPS to maintain the sum
                            setFoOps(opsPerSecond - newCmOps);
                        }}
                        required
                        style={{ width: '25%' }}
                    />
                </div>

                {/* Link Type (Radio Buttons) */}
                <div>
                    <label>Link Type: </label>
                    <label>
                        <input
                            type="radio"
                            name="linkType"
                            value="Direct"
                            checked={linkType === 'Direct'}
                            onChange={() => setLinkType('Direct')}
                            required
                        />
                        Direct
                    </label>
                    <label style={{ marginLeft: '20px' }}>
                        <input
                            type="radio"
                            name="linkType"
                            value="Switch"
                            checked={linkType === 'Switch'}
                            onChange={() => setLinkType('Switch')}
                        />
                        Switch
                    </label>
                </div>

                {/* Submit Button */}
                <div>
                    <button type="submit" style={{ width: '10%' }}>Submit</button>
                </div>

                {/* Error Message */}
                {error && <div style={{ color: 'red', width: '50%' }}>{error}</div>}
            </div>
        </form>
    );
};

export default TraderSessionForm;