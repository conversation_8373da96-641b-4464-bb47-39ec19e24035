import React, { useState, useEffect } from 'react';
import TraderSessionForm from './TraderSessionForm';
import LoadingSpinner from './components/LoadingSpinner';
import { fetchWithAuth } from './utils/api';

const InputPage = () => {
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [submittedForms, setSubmittedForms] = useState([]);
    const [options, setOptions] = useState({
        traderOptions: [],
        clientOptions: []
    });
    const [traderID, setTraderID] = useState([]);

    // Load submitted forms from localStorage on component mount
    useEffect(() => {
        const savedForms = localStorage.getItem('submittedForms');
        if (savedForms) {
            try {
                setSubmittedForms(JSON.parse(savedForms));
            } catch (error) {
                // Keep error logging for debugging localStorage issues
                console.error('Error parsing saved forms:', error);
                localStorage.removeItem('submittedForms');
            }
        }
    }, []);

    // Save submitted forms to localStorage whenever they change
    useEffect(() => {
        localStorage.setItem('submittedForms', JSON.stringify(submittedForms));
    }, [submittedForms]);

    // Fetch all trader IDs on mount
    useEffect(() => {
        fetchWithAuth('/api/get-trader-ids')
            .then(response => response.json())
            .then(data => {
                setOptions(prev => ({
                    ...prev,
                    traderOptions: data.trader_ids ? data.trader_ids.map(id => ({ value: id, label: id })) : []
                }));
            })
            .catch((error) => {
                console.error('Error fetching Trader IDs:', error);
            });
    }, []);

    // Fetch client IDs when traderID changes
    useEffect(() => {
        if (traderID.length > 0) {
            fetchWithAuth(`/api/get-client-ids/${traderID[0].value}`)
                .then(response => response.json())
                .then(data => {
                    setOptions(prev => ({
                        ...prev,
                        clientOptions: data.client_ids ? data.client_ids.map(id => ({ value: id, label: id })) : []
                    }));
                })
                .catch((error) => {
                    console.error('Error fetching Client IDs:', error);
                });
        } else {
            setOptions(prev => ({ ...prev, clientOptions: [] }));
        }
    }, [traderID]);

    const handleFormSubmit = (formData) => {
        setIsLoading(true);
        // Remove debug logging
        setSubmittedForms((prevForms) => [...prevForms, formData]);
        setIsLoading(false);
    };

    // Function to clear all submitted forms
    const clearSubmittedForms = () => {
        if (window.confirm('Are you sure you want to clear all submitted forms?')) {
            setSubmittedForms([]);
            localStorage.removeItem('submittedForms');
        }
    };

    return (
        <div style={{ position: 'relative' }}>
            {isLoading && <LoadingSpinner />}
            <div style={{ 
                display: 'grid', 
                gridTemplateColumns: '1fr 1fr',
                gap: '20px',
                padding: '20px',
                maxWidth: '1600px',
                margin: '0 auto'
            }}>
                {/* Form Section */}
                <div style={{ 
                    background: '#fff',
                    padding: '20px',
                    borderRadius: '8px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                    <h2 style={{ 
                        textAlign: 'center',
                        marginBottom: '20px',
                        color: '#333'
                    }}>Input Page</h2>
                    <TraderSessionForm
                        onSubmit={handleFormSubmit}
                        error={error}
                        setError={setError}
                        options={options}
                        setTraderID={setTraderID}
                    />
                </div>

                {/* Submitted Forms Section */}
                <div style={{ 
                    background: '#fff',
                    padding: '20px',
                    borderRadius: '8px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                    <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        marginBottom: '20px'
                    }}>
                        <h3 style={{ 
                            textAlign: 'center',
                            color: '#333',
                            margin: 0
                        }}>Submitted Forms</h3>
                        {submittedForms.length > 0 && (
                            <button 
                                onClick={clearSubmittedForms}
                                style={{
                                    padding: '5px 10px',
                                    backgroundColor: '#ff4444',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer'
                                }}
                            >
                                Clear All
                            </button>
                        )}
                    </div>
                    {submittedForms.length === 0 ? (
                        <p style={{ 
                            textAlign: 'center',
                            color: '#666',
                            fontStyle: 'italic'
                        }}>No forms submitted yet.</p>
                    ) : (
                        <div style={{ 
                            display: 'grid',
                            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                            gap: '15px',
                            overflowY: 'auto',
                            maxHeight: 'calc(100vh - 200px)',
                            padding: '10px'
                        }}>
                            {submittedForms.map((form, index) => (
                                <div key={index} style={{ 
                                    background: '#f8f9fa',
                                    padding: '15px',
                                    borderRadius: '6px',
                                    border: '1px solid #e9ecef',
                                    boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
                                }}>
                                    <div style={{ 
                                        display: 'grid',
                                        gridTemplateColumns: 'auto 1fr',
                                        gap: '8px',
                                        fontSize: '14px'
                                    }}>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>Client ID:</span>
                                        <span>{form.clientID}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>Trader ID:</span>
                                        <span>{form.traderID}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>Rack ID:</span>
                                        <span>{form.rackID}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>Firm Name:</span>
                                        <span>{form.firmName}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>Tap IP:</span>
                                        <span>{form.tapIP}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>OPS per Second:</span>
                                        <span>{form.opsPerSecond}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>Start Date:</span>
                                        <span>{form.startDate}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>End Date:</span>
                                        <span>{form.endDate || 'NULL'}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>FO_OPS:</span>
                                        <span>{form.foOps}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>CM_OPS:</span>
                                        <span>{form.cmOps}</span>
                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>Link Type:</span>
                                        <span>{form.linkType}</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default InputPage;