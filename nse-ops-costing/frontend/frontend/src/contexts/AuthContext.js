import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext(null);
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL; // Define base URL

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const checkAuth = async () => {
            setLoading(true);
            const refreshToken = localStorage.getItem('refreshToken');

            if (!refreshToken) {
                // No refresh token, user is not logged in
                setUser(null);
                localStorage.removeItem('accessToken'); // Ensure access token is also cleared
                setLoading(false);
                return;
            }

            // If refreshToken exists, try to refresh the session
            try {
                console.log("Found refresh token, attempting session refresh...");
                const response = await axios.post(`${API_BASE_URL}/api/auth/refresh-token`, {
                    refreshToken
                });

                // Expecting { accessToken, refreshToken, user } from refresh endpoint
                const { accessToken, refreshToken: newRefreshToken, user } = response.data;
                localStorage.setItem('accessToken', accessToken);
                localStorage.setItem('refreshToken', newRefreshToken);
                setUser(user);
                console.log("Session refreshed successfully.");

            } catch (refreshError) {
                console.error('Auth refresh error on load:', refreshError.response?.data || refreshError.message);
                // If refresh fails, clear tokens and user
                localStorage.removeItem('accessToken');
                localStorage.removeItem('refreshToken');
                setUser(null);
            } finally {
                setLoading(false);
            }
        };

        checkAuth();
    }, []);

    const login = async (username, password) => {
        try {
            const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
                username,
                password
            });

            // Destructure token (from API) and refreshToken
            const { token, refreshToken, user } = response.data;
            // Store the API's 'token' as 'accessToken' in localStorage
            localStorage.setItem('accessToken', token);
            localStorage.setItem('refreshToken', refreshToken);
            setUser(user);
            setError(null);
            return true;
        } catch (error) {
            console.error('Login API error:', error.response?.data || error.message); // Log login errors
            setError(error.response?.data?.error || 'Login failed');
            return false;
        }
    };

    const logout = async () => {
        try {
            const accessToken = localStorage.getItem('accessToken');
            if (accessToken) {
                await axios.post(`${API_BASE_URL}/api/auth/logout`, null, { // Use base URL
                    headers: { Authorization: `Bearer ${accessToken}` }
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            setUser(null);
        }
    };

    const requestPasswordReset = async (email) => {
        try {
            await axios.post(`${API_BASE_URL}/api/auth/request-password-reset`, { email }); // Use base URL
            return true;
        } catch (error) {
            setError(error.response?.data?.error || 'Failed to request password reset');
            return false;
        }
    };

    const resetPassword = async (token, newPassword) => {
        try {
            await axios.post(`${API_BASE_URL}/api/auth/reset-password`, { // Use base URL
                token,
                newPassword
            });
            return true;
        } catch (error) {
            setError(error.response?.data?.error || 'Failed to reset password');
            return false;
        }
    };

    const value = {
        user,
        loading,
        error,
        login,
        logout,
        requestPasswordReset,
        resetPassword
    };

    return (
        <AuthContext.Provider value={value}>
            {!loading && children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}; 