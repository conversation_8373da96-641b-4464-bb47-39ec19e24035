import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

const Header = () => {
    const navigate = useNavigate();
    const [username, setUsername] = useState('');

    useEffect(() => {
        const token = localStorage.getItem('token');
        if (token) {
            try {
                const decodedToken = atob(token);
                const [username] = decodedToken.split(':');
                setUsername(username);
            } catch (error) {
                // Keep error logging for debugging token issues
                console.error('Error decoding token:', error);
                localStorage.removeItem('token');
                localStorage.removeItem('username');
            }
        }
    }, []);

    const handleLogout = () => {
        localStorage.removeItem('token');
        localStorage.removeItem('username');
        navigate('/login');
    };

    return (
        <header style={{ 
            backgroundColor: '#f8f9fa', 
            padding: '10px', 
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
        }}>
            <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                width: '100%',
                alignItems: 'center',
                marginBottom: '10px'
            }}>
                <h1 style={{ margin: 0 }}>NSE OPS Costing</h1>
                {username && (
                    <span style={{ 
                        fontSize: '16px',
                        color: '#666'
                    }}>
                        Hello, {username}
                    </span>
                )}
            </div>
            <nav style={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                gap: '10px',
                flexWrap: 'wrap'
            }}>
                <Link to="/" style={{ 
                    padding: '8px 16px',
                    textDecoration: 'none', 
                    color: 'blue',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    backgroundColor: 'white'
                }}>Input</Link>
                <Link to="/ops-price" style={{ 
                    padding: '8px 16px',
                    textDecoration: 'none', 
                    color: 'blue',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    backgroundColor: 'white'
                }}>View Bills</Link>
                <Link to="/edit-sessions" style={{ 
                    padding: '8px 16px',
                    textDecoration: 'none', 
                    color: 'blue',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    backgroundColor: 'white'
                }}>Edit Sessions</Link>
                <button 
                    onClick={handleLogout}
                    style={{
                        padding: '8px 16px',
                        backgroundColor: '#dc3545',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '14px'
                    }}
                >
                    Logout
                </button>
            </nav>
        </header>
    );
};

export default Header;