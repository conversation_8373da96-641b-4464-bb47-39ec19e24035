import React, { useState, useEffect } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import '@ag-grid-community/styles/ag-grid.css';
import '@ag-grid-community/styles/ag-theme-alpine.css';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { ModuleRegistry } from '@ag-grid-community/core';
import { fetchWithAuth } from './utils/api';
import LoadingSpinner from './components/LoadingSpinner';
//import { MenuModule } from '@ag-grid-enterprise/menu';
//import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
//import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel';
//import { SetFilterModule } from '@ag-grid-enterprise/set-filter';

const OpsPriceGrid = () => {
    const [rowData, setRowData] = useState([]);
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);

    const handleRecalculate = () => {
        if (!fromDate || !toDate) {
            alert('Please select both From and To dates.');
            return;
        }

        setIsLoading(true);
        fetchWithAuth('/api/calculate_daily_sessions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                from_date: fromDate.toISOString().split('T')[0],
                to_date: toDate.toISOString().split('T')[0]
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.text();
        })
        .then(data => {
            alert('Recalculation Successful!');
        })
        .catch(error => {
            console.error('Error recalculating data:', error);
            alert('Recalculation failed. Please try again.');
        })
        .finally(() => setIsLoading(false));
    };

    const handleFetchResults = () => {
        if (!fromDate || !toDate) {
            alert('Please select both From and To dates.');
            return;
        }

        setIsLoading(true);
        const queryParams = new URLSearchParams({
            from_date: fromDate.toISOString().split('T')[0],
            to_date: toDate.toISOString().split('T')[0]
        });

        fetchWithAuth(`/api/fetch_ops_price?${queryParams}`)
        .then(response => response.json())
        .then(data => setRowData(data))
        .catch(error => {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data');
        })
        .finally(() => setIsLoading(false));
    };

    const columnDefs = [
        { headerName: 'Trader ID', field: 'Trader_ID', sortable: true, filter: true },
        { headerName: 'Session ID', field: 'Session_ID', sortable: true, filter: true },
        { headerName: 'Rack', field: 'Rack', sortable: true, filter: true },
        { headerName: 'Firm', field: 'Firm', sortable: true, filter: true },
        { headerName: 'Tap_IP', field: 'Tap_IP', sortable: true, filter: true },
        { headerName: 'OPS', field: 'OPS', sortable: true, filter: true },
        { 
            headerName: 'Date', 
            field: 'Date', 
            sortable: true, 
            filter: true,
            valueFormatter: (params) => {
                if (params.value) {
                    return params.value.split('T')[0];
                }
                return '';
            }
        },
        { headerName: 'Bill', field: 'Bill', sortable: true, filter: true, aggFunc: 'sum' },
    ];

    const defaultColDef = {
        flex: 1,
        minWidth: 150,
        editable: false,
        resizable: true,
        enableRowGroup: true,
    };

    const autoGroupColumnDef = {
        headerName: 'Group',
        cellRenderer: 'agGroupCellRenderer',
        field: 'Trader_ID',
        minWidth: 250,
    };

    useEffect(() => {
        const today = new Date().toISOString().split('T')[0];
        const queryParams = new URLSearchParams({
            from_date: today,
            to_date: today
        });

        fetchWithAuth(`/api/fetch_ops_price?${queryParams}`)
        .then(response => response.json())
        .then(data => setRowData(data))
        .catch(error => {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data');
        });
    }, []);

    return (
        <div style={{ position: 'relative' }}>
            {isLoading && <LoadingSpinner />}
            <div>
                <div style={{ 
                    marginBottom: '20px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '10px',
                    maxWidth: '400px'
                }}>
                    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                        <label style={{ minWidth: '80px' }}>From Date: </label>
                        <DatePicker 
                            selected={fromDate} 
                            onChange={(date) => setFromDate(date)} 
                            dateFormat="yyyy-MM-dd" 
                            style={{ width: '150px' }}
                        />
                    </div>
                    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                        <label style={{ minWidth: '80px' }}>To Date: </label>
                        <DatePicker 
                            selected={toDate} 
                            onChange={(date) => setToDate(date)} 
                            dateFormat="yyyy-MM-dd" 
                            style={{ width: '150px' }}
                        />
                    </div>
                    <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
                        <button 
                            onClick={handleRecalculate}
                            style={{
                                padding: '8px 16px',
                                backgroundColor: '#28a745',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                fontSize: '14px',
                                width: 'auto',
                                minWidth: '120px'
                            }}
                        >
                            Recalculate
                        </button>
                        <button 
                            onClick={handleFetchResults}
                            style={{
                                padding: '8px 16px',
                                backgroundColor: '#007bff',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                fontSize: '14px',
                                width: 'auto',
                                minWidth: '120px'
                            }}
                        >
                            Fetch Results
                        </button>
                    </div>
                </div>
                <div className="ag-theme-alpine" style={{ height: 600, width: '100%' }}>
                    <AgGridReact
                        rowData={rowData}
                        columnDefs={columnDefs}
                        defaultColDef={defaultColDef}
                        autoGroupColumnDef={autoGroupColumnDef}
                        modules={[ClientSideRowModelModule, RowGroupingModule]}
                        animateRows={true}
                        rowGroupPanelShow="always"
                        pivotPanelShow="always"
                        suppressAggFuncInHeader={true}
                        groupSuppressBlankHeader={true}
                    />
                </div>
            </div>
        </div>
    );
};

export default OpsPriceGrid;
