image: atlassian/default-image:3

definitions:
  services:
    docker:
      memory: 2048
  steps:
    - step: &sonarqube-analysis
        name: "Static Code Analysis"
        script:
          - pipe: sonarsource/sonarqube-scan:1.0.0
            variables:
              SONAR_HOST_URL: ${SONAR_HOST_URL}
              SONAR_TOKEN: ${SONAR_TOKEN}
        after-script:
          - pipe: atlassian/bitbucket-code-insights:0.8.0
            variables:
              REPORT_TITLE: "SonarQube Code Analysis"
              REPORT_KEY: "sonarqube-code-analysis"
              REPORT_TYPE: "security"
              ANNOTATIONS_DATA_FILE: "sonar-report.json"
              ANNOTATIONS_FORMAT: "sonarqube"
    - step: &security-scan
        name: "Security Scan"
        script:
          - pipe: aquasecurity/trivy-pipe:1.0.0
            variables:
              IMAGE: $AWS_REGISTRY_URL/$IMAGE_NAME:$BITBUCKET_COMMIT
              SEVERITY: 'CRITICAL,HIGH'
              FORMAT: 'json'
              OUTPUT: 'trivy-results.json'
        after-script:
          - pipe: atlassian/bitbucket-code-insights:0.8.0
            variables:
              REPORT_TITLE: "Trivy Security Scan"
              REPORT_KEY: "trivy-security-scan"
              REPORT_TYPE: "security"
              ANNOTATIONS_DATA_FILE: "trivy-results.json"
              ANNOTATIONS_FORMAT: "trivy"

pipelines:
  default:
    - step:
        name: "Code Quality Check"
        script:
          - npm install
          - npm run lint
          - npm test
        after-script:
          - pipe: atlassian/bitbucket-code-insights:0.8.0
            variables:
              REPORT_TITLE: "Code Quality Check"
              REPORT_KEY: "code-quality-check"
              REPORT_TYPE: "quality"
              RESULT_ANNOTATIONS: "[{\"path\":\"package.json\",\"line\":1,\"message\":\"Code quality check completed\",\"severity\":\"LOW\"}]"
    - step: *sonarqube-analysis
    - step:
        name: "Build with Buildpacks"
        services:
          - docker
        script:
          - pipe: docker://buildpacks/pack:latest
            variables:
              BUILDER: 'paketobuildpacks/builder:base'
              IMAGE: $AWS_REGISTRY_URL/$IMAGE_NAME:$BITBUCKET_COMMIT
              PATH: './frontend/frontend'
    - step: *security-scan
    - step:
        name: "Push to AWS ECR"
        services:
          - docker
        script:
          - pipe: atlassian/aws-ecr-push-image:1.5.0
            variables:
              AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
              AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
              AWS_DEFAULT_REGION: $AWS_REGION
              IMAGE_NAME: $IMAGE_NAME
              TAGS: $BITBUCKET_COMMIT
    - step:
        name: "Update K8s Manifests"
        script:
          - sed -i "s|image:.*|image: $AWS_REGISTRY_URL/$IMAGE_NAME:$BITBUCKET_COMMIT|" frontend/frontend/deployment.yaml
          - pipe: atlassian/aws-s3-deploy:1.1.0
            variables:
              AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
              AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
              AWS_DEFAULT_REGION: $AWS_REGION
              S3_BUCKET: $ARGOCD_MANIFESTS_BUCKET
              LOCAL_PATH: 'frontend/frontend/deployment.yaml'
        after-script:
          - pipe: atlassian/bitbucket-code-insights:0.8.0
            variables:
              REPORT_TITLE: "Deployment Status"
              REPORT_KEY: "deployment-status"
              REPORT_TYPE: "deployment"
              RESULT_VALUE: "successful"
              RESULT_DESCRIPTION: "Deployment manifest updated and pushed to ArgoCD"