const express = require('express');
const mysql = require('mysql2');
const bodyParser = require('body-parser');
const cors = require('cors');

const app = express();
app.use(bodyParser.json());
app.use(cors()); // Enable CORS

const db = mysql.createConnection({
    host: "**********", //'mysql-service',
    user: 'root',
    password: 'strong_password',
    database: 'nse_costing',
    port: 30006 //3306
});

// API to fetch Trader IDs and Session IDs
app.get('/api/get-ids', (req, res) => {
    // Fetch Trader IDs
    db.query('CALL get_distinct_trader_ids()', (err, results) => {
        if (err) {
            console.error('Error fetching Trader IDs:', err);
            return res.status(500).json({ error: 'Failed to fetch Trader IDs' });
        }

        const traderIDs = results[0].map((row) => row.trader_id);
        res.json({ trader_ids: traderIDs });
    });
});

// API to fetch Session IDs for a specific Trader ID
app.get('/api/get-session-ids/:traderId', (req, res) => {
    const traderId = req.params.traderId;
    
    const query = 'SELECT DISTINCT Session_ID FROM TraderSessions WHERE Trader_ID = ?';
    
    db.query(query, [traderId], (err, results) => {
        if (err) {
            console.error('Error fetching Session IDs:', err);
            return res.status(500).json({ error: 'Failed to fetch Session IDs' });
        }
        
        const sessionIDs = results.map(row => row.Session_ID);
        res.json({ session_ids: sessionIDs });
    });
});

// API to fetch Rack IDs for a specific Trader ID and Session ID combination
app.get('/api/get-rack-ids/:traderId/:sessionId', (req, res) => {
    const { traderId, sessionId } = req.params;
    
    const query = 'SELECT DISTINCT Rack_ID FROM TraderSessions WHERE Trader_ID = ? AND Session_ID = ?';
    
    db.query(query, [traderId, sessionId], (err, results) => {
        if (err) {
            console.error('Error fetching Rack IDs:', err);
            return res.status(500).json({ error: 'Failed to fetch Rack IDs' });
        }
        
        const rackIDs = results.map(row => row.Rack_ID);
        res.json({ rack_ids: rackIDs });
    });
});

// API to fetch Firm Names for a specific Trader ID, Session ID, and Rack ID combination
app.get('/api/get-firm-names/:traderId/:sessionId/:rackId', (req, res) => {
    const { traderId, sessionId, rackId } = req.params;
    
    const query = 'SELECT DISTINCT Firm_Name FROM TraderSessions WHERE Trader_ID = ? AND Session_ID = ? AND Rack_ID = ?';
    
    db.query(query, [traderId, sessionId, rackId], (err, results) => {
        if (err) {
            console.error('Error fetching Firm Names:', err);
            return res.status(500).json({ error: 'Failed to fetch Firm Names' });
        }
        
        const firmNames = results.map(row => row.Firm_Name);
        res.json({ firm_names: firmNames });
    });
});

// API to fetch Tap IPs for a specific combination
app.get('/api/get-tap-ips/:traderId/:sessionId/:rackId/:firmName', (req, res) => {
    const { traderId, sessionId, rackId, firmName } = req.params;
    
    const query = 'SELECT DISTINCT Tap_IP FROM TraderSessions WHERE Trader_ID = ? AND Session_ID = ? AND Rack_ID = ? AND Firm_Name = ?';
    
    db.query(query, [traderId, sessionId, rackId, firmName], (err, results) => {
        if (err) {
            console.error('Error fetching Tap IPs:', err);
            return res.status(500).json({ error: 'Failed to fetch Tap IPs' });
        }
        
        const tapIPs = results.map(row => row.Tap_IP);
        res.json({ tap_ips: tapIPs });
    });
});

// API to handle form submission
app.post('/api/submit-form', (req, res) => {
    const formData = req.body;

    const endDate = formData.endDate === '' ? null : formData.endDate;

    // Call stored procedure to insert data
    db.query(
        'CALL insert_data(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
            formData.traderID,
            formData.sessionID,
            formData.rackID,
            formData.firmName,
            formData.tapIP,
            formData.opsPerSecond,
            formData.startDate,
            endDate,
            formData.foOps,
            formData.cmOps,
        ],
        (err, results) => {
            if (err) {
                console.error('Error inserting data:', err);
                return res.status(500).json({ error: 'Failed to insert data' });
            }
            
            res.json({ status: 'success' });
        }
    );
});

// API to fetch sessions with filters
app.get('/api/fetch-sessions', (req, res) => {
    const {
        traderID,
        sessionID,
        rackID,
        firmName,
        tapIP,
        startDate,
        endDate,
        activeOnly
    } = req.query;

    let query = `
        SELECT 
            Trader_ID,
            Session_ID,
            Rack_ID,
            Firm_Name,
            Tap_IP,
            ops_per_second,
            DATE_FORMAT(Start_Date, '%Y-%m-%d') as Start_Date,
            FO_OPS,
            CM_OPS,
            DATE_FORMAT(End_Date, '%Y-%m-%d') as End_Date
        FROM TraderSessions 
        WHERE 1=1
    `;
    const params = [];

    // Add active sessions filter if requested
    if (activeOnly === 'true') {
        query += ' AND End_Date IS NULL';
    }

    // Build the WHERE clause based on provided filters
    if (traderID) {
        const traderIDs = traderID.split('|');
        query += ' AND Trader_ID IN (?)';
        params.push(traderIDs);
    }

    if (sessionID) {
        query += ' AND Session_ID = ?';
        params.push(sessionID);
    }

    if (rackID) {
        query += ' AND Rack_ID = ?';
        params.push(rackID);
    }

    if (firmName) {
        query += ' AND Firm_Name = ?';
        params.push(firmName);
    }

    if (tapIP) {
        query += ' AND Tap_IP = ?';
        params.push(tapIP);
    }

    if (startDate) {
        query += ' AND Start_Date >= ?';
        params.push(startDate);
    }

    if (endDate) {
        query += ' AND End_Date <= ?';
        params.push(endDate);
    }

    // Add ORDER BY clause for better organization
    query += ' ORDER BY Start_Date DESC';

    db.query(query, params, (err, results) => {
        if (err) {
            console.error('Error fetching sessions:', err);
            return res.status(500).json({ error: 'Failed to fetch sessions' });
        }

        // Log the results for debugging
        console.log('Fetched sessions:', results);

        res.json(results);
    });
});

// API to update session details
app.post('/api/update-session', (req, res) => {
    const { updatedData, originalData } = req.body;

    // Validate required fields
    if (!updatedData || !originalData) {
        return res.status(400).json({ error: 'Both updated and original data are required' });
    }

    // Validate FO_OPS + CM_OPS = ops_per_second
    if (updatedData.FO_OPS + updatedData.CM_OPS !== updatedData.ops_per_second) {
        return res.status(400).json({ error: 'FO_OPS + CM_OPS must equal ops_per_second' });
    }

    const query = `
        UPDATE TraderSessions 
        SET 
            Trader_ID = ?,
            Session_ID = ?,
            Rack_ID = ?,
            Firm_Name = ?,
            Tap_IP = ?,
            ops_per_second = ?,
            Start_Date = ?,
            FO_OPS = ?,
            CM_OPS = ?,
            End_Date = ?
        WHERE 
            Trader_ID = ? AND 
            Session_ID = ? AND 
            Rack_ID = ? AND 
            Firm_Name = ? AND 
            Tap_IP = ? AND 
            Start_Date = ? AND
            (End_Date IS NULL AND ? IS NULL OR End_Date = ?)
    `;

    db.query(
        query,
        [
            // Updated values
            updatedData.Trader_ID,
            updatedData.Session_ID,
            updatedData.Rack_ID,
            updatedData.Firm_Name,
            updatedData.Tap_IP,
            updatedData.ops_per_second,
            updatedData.Start_Date,
            updatedData.FO_OPS,
            updatedData.CM_OPS,
            updatedData.End_Date || null,
            // Original values for WHERE clause (using only the key fields)
            originalData.Trader_ID,
            originalData.Session_ID,
            originalData.Rack_ID,
            originalData.Firm_Name,
            originalData.Tap_IP,
            originalData.Start_Date,
            originalData.End_Date || null,  // For NULL comparison in End_Date
            originalData.End_Date || null   // For direct comparison in End_Date
        ],
        (err, results) => {
            if (err) {
                console.error('Error updating session:', err);
                return res.status(500).json({ error: 'Failed to update session' });
            }

            if (results.affectedRows === 0) {
                return res.status(404).json({ error: 'Session not found or no changes made' });
            }

            if (results.affectedRows > 1) {
                console.error('Warning: Multiple rows were updated');
            }

            res.json({ status: 'success' });
        }
    );
});

app.post('/api/calculate_daily_sessions', (req, res) => {
    const { from_date, to_date } = req.body;

    const query = `
        CALL calculate_daily_sessions(?, ?);
    `;

    db.query(query, [from_date, to_date], (error) => {
        if (error) {
            console.error('Error executing query:', error);
            res.status(500).send('Error executing query');
        } else {
            res.send('Recalculation successful');
        }
    });
});

app.post('/api/fetch_ops_price', (req, res) => {
    const { from_date, to_date } = req.body;
    	
    const query = `
        CALL fetch_ops_price(?, ?);
    `;

    db.query(query, [from_date, to_date], (error, results) => {
        if (error) {
            console.error('Error executing query:', error);
            res.status(500).send('Error executing query');
        } else {
            res.send(results[0]);
        }
    });
});

const port = 3001;
app.listen(port, () => {
    console.log(`Server running on port ${port}`);
});

